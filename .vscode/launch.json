{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug unit tests in library 'postman2openapi'",
      "cargo": {
        "args": ["test", "--no-run", "--lib", "--package=postman2openapi"],
        "filter": {
          "name": "postman2openapi",
          "kind": "lib"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug integration tests",
      "cargo": {
        "args": ["test", "--test", "integration_tests"],
        "filter": {
          "name": "integration_tests",
          "kind": "test"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    },
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug executable 'postman2openapi'",
      "cargo": {
        "args": [
          "build",
          "--package=postman2openapi-cli",
          "--bin=postman2openapi"
        ],
        "filter": {
          "name": "postman2openapi",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
