{"version": "2.0.0", "tasks": [{"command": "cargo", "args": ["test", "--test", "integration_tests"], "problemMatcher": ["$rustc"], "group": "test", "label": "rust: cargo test --test integration_tests"}, {"type": "shell", "command": "wasm-pack", "args": ["test", "--node", "--test", "wasm_node"], "problemMatcher": ["$rustc"], "group": "test", "label": "rust: wasm-pack test --node"}, {"type": "shell", "command": "wasm-pack", "args": ["test", "--headless", "--chrome", "--test", "wasm_browser"], "problemMatcher": ["$rustc"], "group": "test", "label": "rust: wasm-pack test --chrome"}, {"type": "shell", "command": "wasm-pack", "args": ["test", "--headless", "--firefox", "--test", "wasm_browser"], "problemMatcher": ["$rustc"], "group": "test", "label": "rust: wasm-pack test --firefox"}, {"command": "cargo", "args": ["clippy", "--", "-D", "warnings"], "problemMatcher": ["$rustc"], "group": "test", "label": "rust: cargo clippy"}, {"type": "cargo", "command": "build", "problemMatcher": ["$rustc"], "group": {"kind": "build", "isDefault": true}, "label": "rust: cargo build"}, {"type": "cargo", "command": "test", "problemMatcher": ["$rustc"], "group": {"kind": "test", "isDefault": true}, "label": "rust: cargo test"}]}