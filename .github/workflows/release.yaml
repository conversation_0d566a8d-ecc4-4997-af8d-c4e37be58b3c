# Stolen from BurntSushi/ripgrep. :D
#
# The way this works is a little weird. But basically, the create-release job
# runs purely to initialize the GitHub release itself. Once done, the upload
# URL of the release is saved as an artifact.
#
# The build-release job runs only once create-release is finished. It gets
# the release upload URL by downloading the corresponding artifact (which was
# uploaded by create-release). It then builds the release executables for each
# supported platform and attaches them as release assets to the previously
# created release.
#
# The key here is that we create the release only once.

name: release
on:
  push:
    # Enable when testing release infrastructure on a branch.
    # branches:
    # - ag/release
    tags:
      - "[0-9]+.[0-9]+.[0-9]+**"
jobs:
  create-release:
    name: create-release
    runs-on: ubuntu-latest
    # env:
    # Set to force version number, e.g., when no tag exists.
    # POSTMAN2OPENAPI_VERSION: TEST-0.0.0
    steps:
      - name: Create artifacts directory
        run: mkdir artifacts

      - name: Get the release version from the tag
        if: env.POSTMAN2OPENAPI_VERSION == ''
        run: |
          # Apparently, this is the right way to get a tag name. Really?
          #
          # See: https://github.community/t5/GitHub-Actions/How-to-get-just-the-tag-name/m-p/32167/highlight/true#M1027
          echo "POSTMAN2OPENAPI_VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV
          source $GITHUB_ENV
          echo "version is: $POSTMAN2OPENAPI_VERSION"

      - name: Create GitHub release
        id: release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ env.POSTMAN2OPENAPI_VERSION }}
          release_name: ${{ env.POSTMAN2OPENAPI_VERSION }}

      - name: Save release upload URL to artifact
        run: echo "${{ steps.release.outputs.upload_url }}" > artifacts/release-upload-url

      - name: Save version number to artifact
        run: echo "${{ env.POSTMAN2OPENAPI_VERSION }}" > artifacts/release-version

      - name: Upload artifacts
        uses: actions/upload-artifact@v1
        with:
          name: artifacts
          path: artifacts

  build-release:
    name: build-release
    needs: ["create-release"]
    runs-on: ${{ matrix.os }}
    env:
      # For some builds, we use cross to test on 32-bit and big-endian
      # systems.
      CARGO: cargo
      # When CARGO is set to CROSS, this is set to `--target matrix.target`.
      TARGET_FLAGS:
      # When CARGO is set to CROSS, TARGET_DIR includes matrix.target.
      TARGET_DIR: ./target
      # Emit backtraces on panics.
      RUST_BACKTRACE: 1
    strategy:
      matrix:
        build: [linux, macos, win-msvc, win-gnu, win32-msvc]
        include:
          - build: linux
            os: ubuntu-latest
            rust: nightly
            target: x86_64-unknown-linux-musl
          - build: macos
            os: macos-latest
            rust: nightly
            target: x86_64-apple-darwin
          - build: win-msvc
            os: windows-2019
            rust: nightly
            target: x86_64-pc-windows-msvc
          - build: win-gnu
            os: windows-2019
            rust: nightly-x86_64-gnu
            target: x86_64-pc-windows-gnu
          - build: win32-msvc
            os: windows-2019
            rust: nightly
            target: i686-pc-windows-msvc

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 1

      # - name: Install packages (Ubuntu)
      # if: matrix.os == 'ubuntu-18.04'
      # run: |
      # ci/ubuntu-install-packages

      # - name: Install packages (macOS)
      # if: matrix.os == 'macos-latest'
      # run: |
      # ci/macos-install-packages

      - name: Install Rust
        id: toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
      - run: rustup override set ${{steps.toolchain.outputs.name}}
      - run: rustup target add ${{ matrix.target }}

      - name: Use Cross
        if: matrix.target != '' && matrix.build != 'wasm32'
        shell: bash
        run: |
          # We used to install 'cross' from master, but it kept failing. So now
          # we build from a known-good version until 'cross' becomes more stable
          # or we find an alternative. Notably, between v0.2.1 and current
          # master (2022-06-14), the number of Cross's dependencies has doubled.
          cargo install --bins --git https://github.com/rust-embedded/cross --tag v0.2.1
          echo "CARGO=cross" >> $GITHUB_ENV
          echo "TARGET_FLAGS=--target ${{ matrix.target }}" >> $GITHUB_ENV
          echo "TARGET_DIR=./target/${{ matrix.target }}" >> $GITHUB_ENV

      - name: Show command used for Cargo
        run: |
          echo "cargo command is: ${{ env.CARGO }}"
          echo "target flag is: ${{ env.TARGET_FLAGS }}"
          echo "target dir is: ${{ env.TARGET_DIR }}"

      - name: Get release download URL
        uses: actions/download-artifact@v1
        with:
          name: artifacts
          path: artifacts

      - name: Set release upload URL and release version
        shell: bash
        run: |
          release_upload_url="$(cat artifacts/release-upload-url)"
          echo "RELEASE_UPLOAD_URL=$release_upload_url" >> $GITHUB_ENV
          release_version="$(cat artifacts/release-version)"
          echo "RELEASE_VERSION=$release_version" >> $GITHUB_ENV
          source $GITHUB_ENV
          echo "release upload url: $RELEASE_UPLOAD_URL"
          echo "release version: $RELEASE_VERSION"

      - name: Build release binary
        run: ${{ env.CARGO }} build --package postman2openapi-cli --verbose --release ${{ env.TARGET_FLAGS }}

      - name: Strip release binary (linux and macos)
        if: matrix.build == 'linux' || matrix.build == 'macos'
        run: strip "target/${{ matrix.target }}/release/postman2openapi"

      - name: Build archive
        shell: bash
        run: |
          outdir="$(ci/cargo-out-dir "${{ env.TARGET_DIR }}")"
          staging="postman2openapi-${{ env.RELEASE_VERSION }}-${{ matrix.target }}"
          mkdir "$staging"/

          cp {README.md,LICENSE} "$staging/"

          if [ "${{ matrix.os }}" = "windows-2019" ]; then
            cp "target/${{ matrix.target }}/release/postman2openapi.exe" "$staging/"
            7z a "$staging.zip" "$staging"
            echo "ASSET=$staging.zip" >> $GITHUB_ENV
          else
            # The man page is only generated on Unix systems. ¯\_(ツ)_/¯
            cp "target/${{ matrix.target }}/release/postman2openapi" "$staging/"
            tar czf "$staging.tar.gz" "$staging"
            echo "ASSET=$staging.tar.gz" >> $GITHUB_ENV
          fi

      - name: Upload release archive
        uses: actions/upload-release-asset@v1.0.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ env.RELEASE_UPLOAD_URL }}
          asset_path: ${{ env.ASSET }}
          asset_name: ${{ env.ASSET }}
          asset_content_type: application/octet-stream
