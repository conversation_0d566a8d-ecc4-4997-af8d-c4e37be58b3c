name: Deploy postman2openapi Web site to GitHub Pages

on:
  workflow_dispatch:
  push:
    branches:
      - main

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Rust
        id: toolchain
        uses: dtolnay/rust-toolchain@1.64.0
      - run: rustup override set ${{steps.toolchain.outputs.name}}
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20.x

      - name: Install wasm-pack
        run: curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh

      - name: Build WebAssembly module
        run: wasm-pack build --release --out-dir ./web/wasm --target bundler

      - name: Install dependencies
        run: npm install --prefix ./web

      - name: Build
        run: npm run build --prefix ./web

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v1
        with:
          path: ./web/dist

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v1
