name: ci

on:
  pull_request:
  push:
    branches:
      - main
  schedule:
    - cron: "00 00 * * *"

jobs:
  test:
    name: test
    env:
      # For some builds, we use cross to test on 32-bit and big-endian
      # systems.
      CARGO: cargo
      # When CARGO is set to CROSS, this is set to `--target matrix.target`.
      TARGET_FLAGS:
      # When CARGO is set to CROSS, TARGET_DIR includes matrix.target.
      TARGET_DIR: ./target
      # Emit backtraces on panics.
      RUST_BACKTRACE: 1

    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        build:
          - stable
          - beta
          - nightly
          - nightly-musl
          - nightly-mips
          - nightly-32
          - macos
          - win-msvc
          - win-gnu
          - msrv
        include:
          - build: wasm32
            os: ubuntu-latest
            rust: nightly
          - build: msrv
            os: ubuntu-latest
            rust: 1.64.0
          - build: stable
            os: ubuntu-latest
            rust: stable
          - build: beta
            os: ubuntu-latest
            rust: beta
          - build: nightly
            os: ubuntu-latest
            rust: nightly
          - build: nightly-musl
            os: ubuntu-latest
            rust: nightly
            target: x86_64-unknown-linux-musl
          - build: nightly-32
            os: ubuntu-latest
            rust: nightly
            target: i686-unknown-linux-gnu
          - build: nightly-mips
            os: ubuntu-latest
            rust: nightly
            target: mips64-unknown-linux-gnuabi64
          - build: macos
            os: macos-latest
            rust: nightly
          - build: win-msvc
            os: windows-2022
            rust: nightly
          - build: win-gnu
            os: windows-2022
            rust: nightly-x86_64-gnu

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Install Rust
        id: toolchain
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
      - run: rustup override set ${{steps.toolchain.outputs.name}}
      - run: rustup component add rustfmt clippy

      - name: Use Cross
        if: matrix.target != '' && matrix.build != 'wasm32'
        run: |
          cargo install cross
          echo "CARGO=cross" >> $GITHUB_ENV
          echo "TARGET_FLAGS=--target ${{ matrix.target }}" >> $GITHUB_ENV
          echo "TARGET_DIR=./target/${{ matrix.target }}" >> $GITHUB_ENV

      - name: Show command used for Cargo
        run: |
          echo "cargo command is: ${{ env.CARGO }}"
          echo "target flag is: ${{ env.TARGET_FLAGS }}"

      - name: Build postman2openapi and all crates
        run: ${{ env.CARGO }} build --verbose --all ${{ env.TARGET_FLAGS }}

      - name: Run tests
        run: ${{ env.CARGO }} test --verbose --all ${{ env.TARGET_FLAGS }}

      - name: Install
        if: matrix.build == 'wasm32'
        run: curl https://rustwasm.github.io/wasm-pack/installer/init.sh -sSf | sh

      - name: Run wasm32 headless Chrome tests
        if: matrix.build == 'wasm32'
        run: wasm-pack test --headless --chrome

      - name: Run wasm32 Node.js tests
        if: matrix.build == 'wasm32'
        run: wasm-pack test --node

  rustfmt-clippy:
    name: rustfmt
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Install Rust
        id: toolchain
        uses: dtolnay/rust-toolchain@stable
      - run: rustup component add rustfmt
      - name: Check formatting
        run: |
          cargo fmt --all -- --check
      - name: Run clippy
        uses: actions-rs/cargo@v1
        with:
          command: clippy
          args: -- -D warnings
