{"info": {"_postman_id": "1fa4b1c7-84b4-44d9-b611-748d2d5a55ab", "name": "Calculator", "description": "\n", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "10354132", "_collection_link": "https://postman.postman.co/workspace/<PERSON>'s-Team-Workspace~5f614b35-0358-4fdd-85d9-72225ed9b063/collection/10354132-1fa4b1c7-84b4-44d9-b611-748d2d5a55ab?source=collection_link"}, "item": [{"name": "CalculatorSoap", "item": [{"name": "Add", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Add"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Add xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Add>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoapBaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoapBaseUrl}}"], "path": ["calculator.asmx"]}, "description": "Adds two integers. This is a test WebService. ©DNE Online"}, "response": [{"name": "Add response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Add"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Add xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Add>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <AddResponse xmlns=\"http://tempuri.org/\">\n      <AddResult>100</AddResult>\n    </AddResponse>\n  </soap:Body>\n</soap:Envelope>\n"}]}, {"name": "Subtract", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Subtract"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Subtract xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Subtract>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoapBaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoapBaseUrl}}"], "path": ["calculator.asmx"]}}, "response": [{"name": "Subtract response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Subtract"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Subtract xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Subtract>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <SubtractResponse xmlns=\"http://tempuri.org/\">\n      <SubtractResult>100</SubtractResult>\n    </SubtractResponse>\n  </soap:Body>\n</soap:Envelope>\n"}]}, {"name": "Multiply", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Multiply"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Multiply xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Multiply>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoapBaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoapBaseUrl}}"], "path": ["calculator.asmx"]}}, "response": [{"name": "Multiply response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Multiply"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Multiply xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Multiply>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <MultiplyResponse xmlns=\"http://tempuri.org/\">\n      <MultiplyResult>100</MultiplyResult>\n    </MultiplyResponse>\n  </soap:Body>\n</soap:Envelope>\n"}]}, {"name": "Divide", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Divide"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Divide xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Divide>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoapBaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoapBaseUrl}}"], "path": ["calculator.asmx"]}}, "response": [{"name": "Divide response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Divide"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <Divide xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Divide>\n  </soap:Body>\n</soap:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n  <soap:Body>\n    <DivideResponse xmlns=\"http://tempuri.org/\">\n      <DivideResult>100</DivideResult>\n    </DivideResponse>\n  </soap:Body>\n</soap:Envelope>\n"}]}]}, {"name": "CalculatorSoap12", "item": [{"name": "Add", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Add"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Add xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Add>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoap12BaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoap12BaseUrl}}"], "path": ["calculator.asmx"]}, "description": "Adds two integers. This is a test WebService. ©DNE Online"}, "response": [{"name": "Add response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Add"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Add xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Add>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <AddResponse xmlns=\"http://tempuri.org/\">\n      <AddResult>100</AddResult>\n    </AddResponse>\n  </soap12:Body>\n</soap12:Envelope>\n"}]}, {"name": "Subtract", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Subtract"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Subtract xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Subtract>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoap12BaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoap12BaseUrl}}"], "path": ["calculator.asmx"]}}, "response": [{"name": "Subtract response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Subtract"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Subtract xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Subtract>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <SubtractResponse xmlns=\"http://tempuri.org/\">\n      <SubtractResult>100</SubtractResult>\n    </SubtractResponse>\n  </soap12:Body>\n</soap12:Envelope>\n"}]}, {"name": "Multiply", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Multiply"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Multiply xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Multiply>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoap12BaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoap12BaseUrl}}"], "path": ["calculator.asmx"]}}, "response": [{"name": "Multiply response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Multiply"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Multiply xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Multiply>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <MultiplyResponse xmlns=\"http://tempuri.org/\">\n      <MultiplyResult>100</MultiplyResult>\n    </MultiplyResponse>\n  </soap12:Body>\n</soap12:Envelope>\n"}]}, {"name": "Divide", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Divide"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Divide xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Divide>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{CalculatorSoap12BaseUrl}}/calculator.asmx", "host": ["{{CalculatorSoap12BaseUrl}}"], "path": ["calculator.asmx"]}}, "response": [{"name": "Divide response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}, {"key": "SOAPAction", "value": "http://tempuri.org/Divide"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <Divide xmlns=\"http://tempuri.org/\">\n      <intA>100</intA>\n      <intB>100</intB>\n    </Divide>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://www.dneonline.com/calculator.asmx", "protocol": "http", "host": ["www", "dneonline", "com"], "path": ["calculator.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <DivideResponse xmlns=\"http://tempuri.org/\">\n      <DivideResult>100</DivideResult>\n    </DivideResponse>\n  </soap12:Body>\n</soap12:Envelope>\n"}]}]}], "variable": [{"key": "CalculatorSoapBaseUrl", "value": "http://www.dneonline.com", "type": "any"}, {"key": "CalculatorSoap12BaseUrl", "value": "http://www.dneonline.com", "type": "any"}]}