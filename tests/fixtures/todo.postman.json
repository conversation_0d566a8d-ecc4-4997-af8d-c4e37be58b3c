{"info": {"_postman_id": "4f3c0bbe-2592-4fe9-937f-a77367b4040a", "name": "Todo Live Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "10354132", "_collection_link": "https://www.postman.com/technology-demo/workspace/integrations-nova-security-api/collection/23437215-4f3c0bbe-2592-4fe9-937f-a77367b4040a?action=share&source=collection_link&creator=10354132"}, "item": [{"name": "/todos", "request": {"method": "GET", "header": [{"key": "if-none-match", "value": "W/\"2-l9Fw4VUO7kr8CvBlt4zaMCqXZ0w\""}, {"key": "cache-control", "value": "no-cache", "disabled": true}], "url": {"raw": "{{url}}/todos", "host": ["{{url}}"], "path": ["todos"]}}, "response": [{"name": "304 Not Modified", "originalRequest": {"method": "GET", "header": [{"key": "if-none-match", "value": "W/\"2-l9Fw4VUO7kr8CvBlt4zaMCqXZ0w\""}], "url": {"raw": "{{url}}/todos", "host": ["{{url}}"], "path": ["todos"]}}, "status": "Not Modified", "code": 304, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "etag", "value": "W/\"2-l9Fw4VUO7kr8CvBlt4zaMCqXZ0w\""}], "cookie": [], "body": "{}"}, {"name": "200 OK", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{url}}/todos", "host": ["{{url}}"], "path": ["todos"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "content-type", "value": "application/json; charset=utf-8"}, {"key": "content-length", "value": "55"}, {"key": "etag", "value": "W/\"37-rCbCk6sMHKufxTlxOZqO2yaOzkw\""}], "cookie": [], "body": "[\n    {\n        \"id\": 1,\n        \"title\": \"create a new app\",\n        \"completed\": false\n    }\n]"}]}, {"name": "/todos", "request": {"method": "POST", "header": [{"key": "origin", "value": "http://localhost:3000"}, {"key": "cache-control", "value": "no-cache", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"create a new app\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/todos", "host": ["{{url}}"], "path": ["todos"]}}, "response": [{"name": "201 Created", "originalRequest": {"method": "POST", "header": [{"key": "origin", "value": "http://localhost:3000"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"create a new app\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/todos", "host": ["{{url}}"], "path": ["todos"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "content-type", "value": "application/json; charset=utf-8"}, {"key": "content-length", "value": "53"}, {"key": "etag", "value": "W/\"35-pt9AScFLjaMH6u4fQd2srp9JL88\""}], "cookie": [], "body": "{\n    \"id\": 1,\n    \"title\": \"create a new app\",\n    \"completed\": false\n}"}]}, {"name": "/todos/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "response": [{"name": "200 OK", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "content-type", "value": "application/json; charset=utf-8"}, {"key": "content-length", "value": "53"}, {"key": "etag", "value": "W/\"35-pt9AScFLjaMH6u4fQd2srp9JL88\""}], "cookie": [], "body": "{\n    \"id\": 1,\n    \"title\": \"create a new app\",\n    \"completed\": false\n}"}, {"name": "404 Not Found", "originalRequest": {"method": "GET", "header": [{"key": "cache-control", "value": "no-cache"}], "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "content-type", "value": "application/json; charset=utf-8"}, {"key": "content-length", "value": "26"}, {"key": "etag", "value": "W/\"1a-hwodwbWGyVn+6gaESAkhR6ThSk4\""}], "cookie": [], "body": "{\n    \"error\": \"Todo not found\"\n}"}]}, {"name": "/todos/:id", "request": {"method": "PUT", "header": [{"key": "origin", "value": "http://localhost:3000"}, {"key": "cache-control", "value": "no-cache", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"title\": \"create a new app\",\n    \"completed\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "response": [{"name": "200 OK", "originalRequest": {"method": "PUT", "header": [{"key": "origin", "value": "http://localhost:3000"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 1,\n    \"title\": \"create a new app\",\n    \"completed\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "content-type", "value": "application/json; charset=utf-8"}, {"key": "content-length", "value": "52"}, {"key": "etag", "value": "W/\"34-jFAjlX/euuAQycOGrGm2Lfnu/Ws\""}], "cookie": [], "body": "{\n    \"id\": 1,\n    \"title\": \"create a new app\",\n    \"completed\": true\n}"}]}, {"name": "/todos/:id", "request": {"method": "DELETE", "header": [{"key": "origin", "value": "http://localhost:3000"}, {"key": "cache-control", "value": "no-cache", "disabled": true}], "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "1"}]}}, "response": [{"name": "204 No Content", "originalRequest": {"method": "DELETE", "header": [{"key": "origin", "value": "http://localhost:3000"}], "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "3"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}], "cookie": [], "body": "{}"}, {"name": "404 Not Found", "originalRequest": {"method": "DELETE", "header": [{"key": "origin", "value": "http://localhost:3000"}, {"key": "cache-control", "value": "no-cache"}], "url": {"raw": "{{url}}/todos/:id", "host": ["{{url}}"], "path": ["todos", ":id"], "variable": [{"key": "id", "value": "3"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "content-type", "value": "application/json; charset=utf-8"}, {"key": "content-length", "value": "26"}, {"key": "etag", "value": "W/\"1a-hwodwbWGyVn+6gaESAkhR6ThSk4\""}], "cookie": [], "body": "{\n    \"error\": \"Todo not found\"\n}"}]}, {"name": "/", "request": {"method": "GET", "header": [{"key": "upgrade-insecure-requests", "value": "1"}, {"key": "if-none-match", "value": "W/\"d24-18a9987fad5\""}, {"key": "cache-control", "value": "no-cache", "disabled": true}], "url": {"raw": "{{url}}/", "host": ["{{url}}"], "path": [""]}}, "response": [{"name": "304 Not Modified", "originalRequest": {"method": "GET", "header": [{"key": "upgrade-insecure-requests", "value": "1"}, {"key": "if-none-match", "value": "W/\"d24-18a9987fad5\""}], "url": {"raw": "{{url}}/", "host": ["{{url}}"], "path": [""]}}, "status": "Not Modified", "code": 304, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "accept-ranges", "value": "bytes"}, {"key": "cache-control", "value": "public, max-age=0"}, {"key": "last-modified", "value": "Fri, 15 Sep 2023 15:49:00 GMT"}, {"key": "etag", "value": "W/\"d24-18a9987fad5\""}], "cookie": [], "body": "{}"}, {"name": "200 OK", "originalRequest": {"method": "GET", "header": [{"key": "upgrade-insecure-requests", "value": "1"}, {"key": "if-none-match", "value": "W/\"d24-18a9987fad5\""}, {"key": "cache-control", "value": "no-cache"}], "url": {"raw": "{{url}}/", "host": ["{{url}}"], "path": [""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "x-powered-by", "value": "Express"}, {"key": "accept-ranges", "value": "bytes"}, {"key": "cache-control", "value": "public, max-age=0"}, {"key": "last-modified", "value": "Fri, 15 Sep 2023 15:49:00 GMT"}, {"key": "etag", "value": "W/\"d24-18a9987fad5\""}, {"key": "content-type", "value": "text/html; charset=UTF-8"}, {"key": "content-length", "value": 3364}], "cookie": [], "body": "{}"}]}], "variable": [{"key": "url", "value": "localhost:3000"}]}