{"info": {"_postman_id": "97d32c90-63fd-4045-8332-d84d854ba6c4", "name": "Postman API", "description": "The Postman API allows you to programmatically access data stored in Postman account with ease.\n\nThe easiest way to get started with the API is to click the [_Run in Postman_](https://www.getpostman.com/docs/run_button_ux) button present at the top of the documentation page and use the Postman App to send requests.\n\n\n# Overview\n\n1. You need a valid <a href=\"#authentication\">API Key</a> to send requests to the API endpoints. You can get your key from the [integrations dashboard](https://go.postman.co/integrations/services/pm_pro_api).\n\n1. The API has an access <a href=\"#rate-limits\">rate limit</a> applied to it.\n\n1. The Postman API will only respond to secured communication done over HTTPS. HTTP requests will be sent a `301` redirect to corresponding HTTPS resources.\n\n1. Response to every request is sent in [JSON format](https://en.wikipedia.org/wiki/JSON). In case the API request results in an error, it is represented by an `\"error\": {}` key in the JSON response.\n\n1. The request method (verb) determines the nature of action you intend to perform. A request made using the `GET` method implies that you want to fetch something from Postman, and `POST` implies you want to save something new to Postman.\n\n1. The API calls will respond with appropriate [HTTP status codes](https://en.wikipedia.org/wiki/List_of_HTTP_status_codes) for all requests. Within Postman Client, when a response is received, the status code is highlighted and is accompanied by a help text that indicates the possible meaning of the response code. A `200 OK` indicates all went well, while `4XX` or `5XX` response codes indicate an error from the requesting client or our API servers respectively.\n\n1. Individual resources in your Postman Account is accessible using its unique id (`uid`). The `uid` is a simple concatenation of the resource owner's user-id and the resource-id. For example, a collection's `uid` is `{{owner_id}}-{{collection_id}}`.\n\n# Authentication\n\nAn API Key is required to be sent as part of every request to the Postman API, in the form of an `X-Api-Key` request header.\n\n> If you do not have an API Key, you can easily generate one by heading over to the [Postman Integrations Dashboard](https://go.postman.co/integrations/services/pm_pro_api).\n\nAn API Key tells our API server that the request it received came from you. Everything that you have access to in Postman is accessible with an API Key that is generated by you.\n\nFor ease of use inside Postman, you could store your API key in an [environment variable](https://www.getpostman.com/docs/environments) called `postman_api_key` and this [Collection](https://www.getpostman.com/docs/collections) will automatically use it to make API calls.\n\n## API Key related error response\n\nIf an API Key is missing, malformed, or invalid, you will receive a `401 Unauthorised` response code and the following JSON response:\n\n```\n{\n  \"error\": { \n    \"name\": \"AuthenticationError\",\n    \"message\": \"API Key missing. Every request requires an API Key to be sent.\"\n  }\n}\n```\n\n## Using the API Key as a query parameter\n\nEvery request that accepts API Key as `X-Api-Key` request header, also accepts the key when sent as `apikey` URL query parameter.\n\nAPI key sent as part of the header has a higher priority in case you send the key using both request header and query parameter.\n\n\n# Rate Limits\n\nAPI access rate limits are applied at a per-key basis in unit time. Access to the API using a key is limited to **60 requests per minute**. In addition, every API response is accompanied by the following set of headers to identify the status of your consumption.\n\n\n| Header                  | Description |\n|-------------------------|-------------|\n| `X-RateLimit-Limit`     | The maximum number of requests that the consumer is permitted to make per minute. |\n| `X-RateLimit-Remaining` | The number of requests remaining in the current rate limit window. |\n| `X-RateLimit-Reset`     | The time at which the current rate limit window resets in UTC epoch seconds. |\n\n\nOnce you hit the rate limit, you will receive a response similar to the following JSON, with a status code of `429 Too Many Requests`.\n\n```json\n{\n  \"error\": {\n    \"name\": \"rateLimitError\",\n    \"message\": \"Rate Limit exceeded. Please retry at 1465452702843\"\n  }\n}\n```\n\n\n# Support\n\nFor help regarding accessing the Postman API, feel free to discuss it in our [Discourse Community](https://community.getpostman.com). You can also drop in a line at [<EMAIL>](mailto:<EMAIL>).\n\nIn the event you receive a `503` response from our servers, it implies that we have hit an unexpected spike in API access traffic and would usually be operational within the next 5 minutes. If the outage persists, or your receive any other form of `5XX` error, kindly let us know.\n\n\n# Terms of Use\n\nFor information on API terms of use and privacy, refer to our policy document at [https://www.getpostman.com/licenses/privacy](https://www.getpostman.com/licenses/privacy)\n\n# API Reference", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Collections", "item": [{"name": "All Collections", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"response is ok\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"response json data should not have errors\", function () {", "    pm.response.to.have.jsonBody();", "    pm.response.to.not.have.jsonBody('error');", "});", "", "pm.test(\"response json should contain collections\", function () {", "    pm.expect(pm.response.json()).to.have.property('collections').and.be.an('array');", "});", "", "// additional tests to ensure that the relevant environment variables are present", "pm.test('\"postman_api_key\" variable should be present', function () {", "    pm.expect(pm.variables.get('postman_api_key')).to.be.a('string');", "});"], "id": "bfaf6f3a-9a23-473f-b8d6-95c8765e9cb3"}}], "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/collections", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"]}, "description": "The `/collections` endpoint returns a list of all [collections](https://www.getpostman.com/docs/collections) that are accessible by you. The list includes your own collections and the collections that you have subscribed to.\n\nThe response contains an array of collection information containing the `name`, `id`, `owner` and `uid` of each collection.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/collections", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "javascript", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "212", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 27 May 2016 21:36:47 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"1ae-4X8iFcujMd+trF9JIw72UA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1464385067", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"collections\":[{\"id\":\"dac5eac9-148d-a32e-b76b-3edee9da28f7\",\"name\":\"Cloud API\",\"owner\":\"631643\",\"uid\":\"631643-dac5eac9-148d-a32e-b76b-3edee9da28f7\"},{\"id\":\"f2e66c2e-5297-e4a5-739e-20cbb90900e3\",\"name\":\"Sample Collection\",\"owner\":\"631643\",\"uid\":\"631643-f2e66c2e-5297-e4a5-739e-20cbb90900e3\"},{\"id\":\"f695cab7-6878-eb55-7943-ad88e1ccfd65\",\"name\":\"Postman Echo\",\"owner\":\"631643\",\"uid\":\"631643-f695cab7-6878-eb55-7943-ad88e1ccfd65\"}]}"}]}, {"name": "Single Collection ", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"response is ok\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"response json data should not have errors\", function () {", "    pm.response.to.have.jsonBody();", "    pm.response.to.not.have.jsonBody('error');", "});", "", "pm.test(\"response json should contain one collection\", function () {", "    pm.expect(pm.response.json()).to.have.property('collection').and.be.an('object');", "});", "", "// additional tests to ensure that the relevant environment variables are present", "pm.test('\"postman_api_key\" variable should be present', function () {", "    pm.expect(pm.variables.get('postman_api_key')).to.be.a('string');", "});", "", "// you can unskip this test in case you want to have a stricter check on the selected", "// environment.", "pm.test.skip('\"collection_uid\" variable should be present', function () {", "    pm.expect(pm.variables.get('collection_uid')).to.be.a('string');", "});"], "id": "5dbfdfe4-9677-45fb-aac1-4e2f87fc4c08"}}], "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}, "description": "Access the contents of a collection that is accessible to you using its unique id (`uid`).\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "javascript", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 27 May 2016 21:37:31 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"453-t7Du3QMg1i7hkk6S4e3NvA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "57", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1464385067", "name": "X-RateLimit-Reset", "description": ""}, {"key": "transfer-encoding", "value": "chunked", "name": "transfer-encoding", "description": ""}], "cookie": [], "body": "{\"collection\":{\"variables\":[],\"info\":{\"name\":\"Sample Collection\",\"_postman_id\":\"f2e66c2e-5297-e4a5-739e-20cbb90900e3\",\"description\":\"This is a sample collection that makes a tiny request to Postman Echo service to get the list of request headers sent by a HTTP client.\",\"schema\":\"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"},\"item\":[{\"id\":\"82ee981b-e19f-962a-401e-ea34ebfb4848\",\"name\":\"Request Headers\",\"event\":[{\"listen\":\"test\",\"script\":{\"type\":\"text/javascript\",\"exec\":\"var responseJSON;\\ntry {\\n    tests[\\\"Body contains headers\\\"] = responseBody.has(\\\"headers\\\");\\n    responseJSON = JSON.parse(responseBody);\\n    tests[\\\"Header contains host\\\"] = \\\"host\\\" in responseJSON.headers;\\n    tests[\\\"Header contains test parameter sent as part of request header\\\"] = \\\"my-sample-header\\\" in responseJSON.headers;\\n}\\ncatch (e) { }\\n\\n\\n\\n\"}}],\"request\":{\"url\":\"https://echo.getpostman.com/headers\",\"method\":\"GET\",\"header\":[{\"key\":\"my-sample-header\",\"value\":\"Lorem ipsum dolor sit amet\",\"description\":\"\"}],\"body\":{\"mode\":\"formdata\",\"formdata\":[]},\"description\":\"\"},\"response\":[]}]}}"}]}, {"name": "Create Collection", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"response is ok\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"response json data should not have errors\", function () {", "    pm.response.to.have.jsonBody();", "    pm.response.to.not.have.jsonBody('error');", "});", "", "pm.test(\"response json should contain one collection\", function () {", "    pm.expect(pm.response.json()).to.have.property('collection').and.be.an('object');", "});", "", "pm.test(\"response json should return collection details\", function () {", "    pm.expect(pm.response.json()).to.have.property('collection')", "        .and.to.include.keys(['id', 'name', 'uid']);", "});", "", "// additional tests to ensure that the relevant environment variables are present", "pm.test('\"postman_api_key\" variable should be present', function () {", "    pm.expect(pm.variables.get('postman_api_key')).to.be.a('string');", "});"], "id": "6b7c2d5f-e24e-4ac4-90b6-900b290acafb"}}], "request": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"info\": {\n      \"name\": \"Sample Collection {{$randomInt}}\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"item\": [\n          {\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"https://postman-echo.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\\"data\\\": \\\"123\\\"}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            }\n          }\n        ]\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"https://postman-echo/get\",\n          \"method\": \"GET\",\n          \"description\": \"This is a sample GET Request\"\n        }\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"]}, "description": "This endpoint allows you to create collections using the Postman Collection v2 format. For more information about the v2 schema, check the format [here](https://schema.getpostman.com/json/collection/v2.0.0/docs/index.html).\n\nOn successful creation of the collection, the response returns the collection `name`, `id` and the `uid`.\n\nYou can also specify the context of a workspace to create a collection in directly by passing the `workspace` as a query param.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Collection Already Exists", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"_postman_id\": \"ac03df1d-90f0-401d-aa57-39c395253c80\",\n      \"name\": \"Sample Collection\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "162", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 11:26:15 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"a2-6Yn0MohF7SWjZhtKpPnEAQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479900431", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"instanceFoundError\",\"message\":\"The specified item already exists.\",\"details\":{\"item\":\"collection\",\"id\":\"ac03df1d-90f0-401d-aa57-39c395253c80\"}}}"}, {"name": "Malformed Request", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "94", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 11:24:15 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"5e-pljLJKY51ijXQuNsHOu0qw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "55", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479900267", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"malformedRequestError\",\"message\":\"Missing required property: name at info\"}}"}, {"name": "Collection Created", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"name\": \"Sample Collection\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "109", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 11:23:49 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"89-p4nfYdFl+5DEKOdB5g399Q\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "57", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479900267", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"collection\":{\"id\":\"2412a72c-1d8e-491b-aced-93809c0e94e9\",\"name\":\"Sample Collection\",\"uid\":\"5852-2412a72c-1d8e-491b-aced-93809c0e94e9\"}}"}, {"name": "Create Collection in a Workspace", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"info\": {\n      \"name\": \"Sample Collection {{$randomInt}}\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"item\": [\n          {\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"https://postman-echo.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\\"data\\\": \\\"123\\\"}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            }\n          }\n        ]\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"https://postman-echo/get\",\n          \"method\": \"GET\",\n          \"description\": \"This is a sample GET Request\"\n        }\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections?workspace={{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections"], "query": [{"key": "workspace", "value": "{{workspace_id}}", "description": "The workspace ID in which the collection is to be created"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "", "type": "text"}], "cookie": [], "body": "{\n\t\"collection\": {\n\t\t\"id\": \"2412a72c-1d8e-491b-aced-93809c0e94e9\",\n    \"name\": \"Sample Collection\",\n    \"uid\": \"5852-2412a72c-1d8e-491b-aced-93809c0e94e9\"\n\t}\n}"}]}, {"name": "Update Collection", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"response is ok\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"response json data should not have errors\", function () {", "    pm.response.to.have.jsonBody();", "    pm.response.to.not.have.jsonBody('error');", "});", "", "pm.test(\"response json should contain one collection\", function () {", "    pm.expect(pm.response.json()).to.have.property('collection').and.be.an('object');", "});", "", "// additional tests to ensure that the relevant environment variables are present", "pm.test('\"postman_api_key\" variable should be present', function () {", "    pm.expect(pm.variables.get('postman_api_key')).to.be.a('string');", "});", "", "// we double check the request to ensure that relevant parameters are present", "pm.test(\"request body must have valid json\", function () {", "    var _ = require('lodash'),", "        requestJSON = JSON.parse(_.get(pm.request, 'body.raw', '{}')),", "        collectionIdInRequestUrl = _.get(pm.request, 'url.path[1]', '').replace(/^.*?-/, '');", "", "    // the request body collection should have the same collection Id as sent in URL", "    pm.expect(requestJSON).to.have.nested.property('collection.info._postman_id', ", "        collectionIdInRequestUrl);", "});", "", "// you can unskip this test in case you want to have a stricter check on the selected", "// environment.", "pm.test.skip('\"collection_uid\" variable should be present', function () {", "    pm.expect(pm.variables.get('collection_uid')).to.be.a('string');", "});"], "id": "39ef9d99-06d2-4877-ab59-8145b78cc540"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"info\": {\n      \"name\": \"Sample Collection {{$randomInt}}\",\n      \"description\": \"This is just a sample collection.\",\n      \"_postman_id\": \"174bad7c-07e3-45f3-914f-36cf84e5586f\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"item\": [\n          {\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"https://postman-echo.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\\"data\\\": \\\"123\\\"}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            }\n          }\n        ]\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"https://postman-echo/get\",\n          \"method\": \"GET\",\n          \"description\": \"This is a sample GET Request\"\n        }\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}, "description": "This endpoint allows you to update an existing collection using the Postman Collection v2 format. For more information about the v2 schema, check the format [here](https://schema.getpostman.com/json/collection/v2.0.0/docs/index.html).\n\nOn successful updation of the collection, the response returns the collection `name`, `id` and `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter.\n\nNote: Please be careful when trying to update the collection, as the existing collection will be replaced by the request body."}, "response": [{"name": "Malformed Request", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"_postman_id\": \"1d3daef4-2037-4584-ab86-bafd8c8f8a55\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"_postman_id\": \"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"This is a second folder\",\n        \"description\": \"This is the second folder\",\n        \"item\": []\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      },\n      {\n        \"name\": \"This is the second request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/post\",\n          \"method\": \"POST\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is the second request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "94", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 12:03:43 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"5e-pljLJKY51ijXQuNsHOu0qw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479902683", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"malformedRequestError\",\"message\":\"Missing required property: name at info\"}}"}, {"name": "Collection ID Mismatch", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"name\": \"Sample Collection\",\n      \"_postman_id\": \"1d3daef4-2037-4584-ab86-bafd8c8f8a54\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"_postman_id\": \"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"This is a second folder\",\n        \"description\": \"This is the second folder\",\n        \"item\": []\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      },\n      {\n        \"name\": \"This is the second request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/post\",\n          \"method\": \"POST\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is the second request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "140", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 12:04:06 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"8c-trpzrQEIYF2JYEPp83AKsw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479902683", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"collectionMismatchError\",\"message\":\"The collection ID in the path does not match the collection ID in the request body.\"}}"}, {"name": "Not Enough Permissions", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"name\": \"Sample Collection\",\n      \"_postman_id\": \"f7e2f1e3-94fb-55a6-e491-ce5cc80aa451\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"_postman_id\": \"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"This is a second folder\",\n        \"description\": \"This is the second folder\",\n        \"item\": []\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      },\n      {\n        \"name\": \"This is the second request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/post\",\n          \"method\": \"POST\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is the second request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "107", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 12:06:17 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"6a-1LtIwVNp8+UcS8eGjj10yg\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479902837", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"forbiddenError\",\"message\":\"You do not have enough permissions to perform this action.\"}}"}, {"name": "Collection Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"name\": \"Sample Collection\",\n      \"_postman_id\": \"1d3daef4-2037-4584-ab86-bafd8c8f8a55\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"_postman_id\": \"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"This is a second folder\",\n        \"description\": \"This is the second folder\",\n        \"item\": []\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      },\n      {\n        \"name\": \"This is the second request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/post\",\n          \"method\": \"POST\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is the second request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "110", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 12:03:23 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"89-Tuf/A5YFyf5ewpKKrb1Zww\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "56", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479902614", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"collection\":{\"id\":\"1d3daef4-2037-4584-ab86-bafd8c8f8a55\",\"name\":\"Sample Collection\",\"uid\":\"5852-1d3daef4-2037-4584-ab86-bafd8c8f8a55\"}}"}, {"name": "Collection Not Found", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"collection\": {\n    \"variables\": [],\n    \"info\": {\n      \"name\": \"Sample Collection\",\n      \"_postman_id\": \"1d3daef4-2037-4584-ab86-bafd8c8f8a54\",\n      \"description\": \"This is just a sample collection.\",\n      \"schema\": \"https://schema.getpostman.com/json/collection/v2.0.0/collection.json\"\n    },\n    \"item\": [\n      {\n        \"name\": \"This is a folder\",\n        \"description\": \"\",\n        \"item\": [\n          {\n            \"_postman_id\": \"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\n            \"name\": \"Sample POST Request\",\n            \"request\": {\n              \"url\": \"echo.getpostman.com/post\",\n              \"method\": \"POST\",\n              \"header\": [\n                {\n                  \"key\": \"Content-Type\",\n                  \"value\": \"application/json\",\n                  \"description\": \"\"\n                }\n              ],\n              \"body\": {\n                \"mode\": \"raw\",\n                \"raw\": \"{\\n\\t\\\"data\\\": \\\"123\\\"\\n}\"\n              },\n              \"description\": \"This is a sample POST Request\"\n            },\n            \"response\": []\n          }\n        ]\n      },\n      {\n        \"name\": \"This is a second folder\",\n        \"description\": \"This is the second folder\",\n        \"item\": []\n      },\n      {\n        \"name\": \"Sample GET Request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/get\",\n          \"method\": \"GET\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is a sample GET Request\"\n        },\n        \"response\": []\n      },\n      {\n        \"name\": \"This is the second request\",\n        \"request\": {\n          \"url\": \"echo.getpostman.com/post\",\n          \"method\": \"POST\",\n          \"header\": [],\n          \"body\": {\n            \"mode\": \"formdata\",\n            \"formdata\": []\n          },\n          \"description\": \"This is the second request\"\n        },\n        \"response\": []\n      }\n    ]\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "153", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 12:04:36 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"a5-drR2WGUHaq2BB+3XLU6ssQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "55", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479902683", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"instanceNotFoundError\",\"message\":\"The specified item does not exist.\",\"details\":{\"item\":\"collection\",\"id\":\"1d3daef4-2037-4584-ab86-bafd8c8f8a54\"}}}"}]}, {"name": "Delete Collection", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test(\"response is ok\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"response json data should not have errors\", function () {", "    pm.response.to.have.jsonBody();", "    pm.response.to.not.have.jsonBody('error');", "});", "", "pm.test(\"response json should contain one collection\", function () {", "    pm.expect(pm.response.json()).to.have.property('collection').and.be.an('object');", "});", "", "pm.test(\"response json should return collection details\", function () {", "    pm.expect(pm.response.json()).to.have.property('collection')", "        .and.to.include.keys(['id', 'uid']);", "});", "", "// additional tests to ensure that the relevant environment variables are present", "pm.test('\"postman_api_key\" variable should be present', function () {", "    pm.expect(pm.variables.get('postman_api_key')).to.be.a('string');", "});"], "id": "2a44e2a3-ab17-45c2-b144-6c0818264836"}}], "request": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}, "description": "This endpoint allows you to delete an existing collection.\n\nOn successful deletion of the collection, the response returns the `id` and `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Collection Deleted", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "92", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 11:45:05 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"6e-6WPh9C5je+q09Mr1L2eTdQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "56", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479901548", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"collection\":{\"id\":\"a14c6da7-afba-4a84-bf22-4febbaaced6c\",\"uid\":\"5852-a14c6da7-afba-4a84-bf22-4febbaaced6c\"}}"}, {"name": "Collection Not Found", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.getpostman.com/collections/{{collection_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "{{collection_uid}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "151", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 11:45:25 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"a5-dInmqa2MlaI3JSR844+prg\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "55", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479901548", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"instanceNotFoundError\",\"message\":\"The specified item does not exist.\",\"details\":{\"item\":\"collection\",\"id\":\"a14c6da7-afba-4a84-bf22-4febbaaced6c\"}}}"}]}, {"name": "Create a Fork", "request": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"Fork name\"\n}"}, "url": {"raw": "https://api.getpostman.com/collections/fork/{{collection_uid}}?workspace={{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "fork", "{{collection_uid}}"], "query": [{"key": "workspace", "value": "{{workspace_id}}", "description": "Workspace ID is required to create a fork"}]}, "description": "This endpoint allows you to create a fork from an existing collection.\n\nOn successful creation of the collection, the response returns the collection `name`, `id`, `uid` along with `fork` information.\n\nYou can also specify the context of a workspace to fork a collection in by passing the `workspace` as a query param.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": []}, {"name": "Merge a Fork", "request": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"strategy\": \"deleteSource\",\n\t\"source\": \"{{source_collection_uid}}\",\n\t\"destination\": \"{{destination_collection_uid}}\"\n}"}, "url": {"raw": "https://api.getpostman.com/collections/merge", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["collections", "merge"]}, "description": "This endpoint allows you to merge a forked collection back to its destination collection.\n\nOn successful creation of the collection, the response returns the collection `name`, `id` and `uid`.\n\nYou need to specify the fork UID (as `source`) and destination collection UID (as `destination`) in the request body.\n\nOptionally, you can also specify the merge strategy as either `deleteSource` or `updateSourceWithDestination`. Following is an explanation of the merge strategies\n\n| Merge Strategy | Behaviour |\n| --- | --- |\n| deleteSource | Forked collection is deleted after merging |\n| updateSourceWithDestination | Forked collection is up to date with changes in destination collection |\n\nIf the collections cannot be merged (due to conflicts), appropriate error messages will be returned.\n\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": []}], "protocolProfileBehavior": {}}, {"name": "Environments", "item": [{"name": "All Environments", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON;", "", "// parse response body as JSON", "try { responseJSON = JSON.parse(responseBody); } catch (e) { }", "tests['received a valid response JSON'] = !!responseJSON;", "!responseJSON && (responseJSON = {});", "", "tests['status code is 200'] = (responseCode.code === 200);", "tests['response does not have errors'] = (!responseJSON.error);", "", "tests['response has an array of environments'] = Array.isArray(responseJSON.environments);"], "id": "02cb7df7-0ca5-4d21-a813-082d03c4e453"}}], "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/environments", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments"]}, "description": "The `/environments` endpoint returns a list of all [environments](https://www.getpostman.com/docs/environments) that belong to you..\n\nThe response contains an array of environments' information containing the `name`, `id`, `owner` and `uid` of each environment.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/environments", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "javascript", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "174", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 27 May 2016 21:30:19 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"137-6vHGuOTB+WAXFzmL5J2roQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1464384679", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"environments\":[{\"id\":\"357668d2-84f1-2264-438b-113095359f80\",\"name\":\"Postman Cloud API\",\"owner\":\"631643\",\"uid\":\"631643-357668d2-84f1-2264-438b-113095359f80\"},{\"id\":\"84a119b6-f4b1-9120-5f11-a73b17818d70\",\"name\":\"Postman Cloud API.template\",\"owner\":\"631643\",\"uid\":\"631643-84a119b6-f4b1-9120-5f11-a73b17818d70\"}]}"}]}, {"name": "Single Environment", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON;", "", "// parse response body as JSON", "try { responseJSON = JSON.parse(responseBody); } catch (e) { }", "tests['received a valid response JSON'] = !!responseJSON;", "!responseJSON && (responseJSON = {});", "", "tests['status code is 200'] = (responseCode.code === 200);", "tests['response does not have errors'] = (!responseJSON.error);", "", "tests['response has an environment object'] = (typeof responseJSON.environment === 'object');"], "id": "c2bf0834-60f6-44fd-a68a-5d642d7a5da0"}}], "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}, "description": "Access the contents of an environment that is accessible to you using its unique id (`uid`).\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "javascript", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "172", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 27 May 2016 21:29:40 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"be-uio3LgF25RzA3k1qO7hl5A\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "57", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1464384588", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"environment\":{\"id\":\"84a119b6-f4b1-9120-5f11-a73b17818d70\",\"name\":\"Postman Cloud API.template\",\"values\":[{\"key\":\"postman_api_key\",\"value\":\"\",\"type\":\"text\",\"enabled\":true,\"hovered\":false}]}}"}]}, {"name": "Create Environment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n    \"environment\": {\n        \"name\": \"Sample Environment Name (required)\",\n        \"values\": [\n            {\"key\": \"variable_name_1\", \"value\": \"The value of the variable\"},\n            {\"key\": \"variable_name_2\", \"value\": \"\"}\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/environments", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments"]}, "description": "A sample body is added to the request that conforms to the following JSON schema:\n\n```json\n{\n  \"type\": \"object\",\n  \"properties\": {\n    \"environment\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"name\": {\n              \"type\": \"string\",\n              \"maxLength\": 254,\n              \"minLength\": 1\n            },\n        \"values\": {\n          \"type\": \"array\",\n          \"maxItems\": 100,\n          \"additionalItems\": false,\n          \"items\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"key\": {\n                  \"type\": \"string\",\n                  \"maxLength\": 254\n                  \"minLength\": 1\n                },\n                \"value\": { \"type\": \"string\" },\n                \"enabled\": { \"type\": \"boolean\" }\n              },\n              \"required\": [\"key\", \"value\"]\n            }\n        }\n      },\n      \"required\": [\"name\"]\n    }\n  },\n  \"required\": [\"environment\"]\n}\n```\n\nOn successful creation of the environment, the API returns the environment name and `id`.\n\nYou can also specify the context of a workspace to create an environment in directly by passing the `workspace` as a query param.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "warning": ""}, {"key": "X-Api-Key", "value": "{{postman_api_key}}", "warning": ""}], "body": {"mode": "raw", "raw": "{\n    \"environment\": {\n        \"name\": \"Sample Environment Name (required)\",\n        \"values\": [\n            {\"key\": \"variable_name_1\", \"value\": \"The value of the variable\"},\n            {\"key\": \"variable_name_2\", \"value\": \"\"}\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/environments", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "126", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Thu, 23 Jun 2016 14:46:23 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"9b-wON61AUt+hEo2p0r4xQg1Q\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1466693243", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"environment\":{\"id\":\"f158266e-306b-4702-a2b9-e4ede7878b7a\",\"name\":\"Sample Environment Name (required)\",\"uid\":\"5665-f158266e-306b-4702-a2b9-e4ede7878b7a\"}}"}, {"name": "Failure Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n    \n}"}, "url": {"raw": "https://api.getpostman.com/environments", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "javascript", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "93", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Mon, 13 Jun 2016 11:11:19 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"5d-zAJI/gvBsRewcmdXsl3iWQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1465816339", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"malformedRequestError\",\"message\":\"Missing required property: environment\"}}"}, {"name": "Create Environment in a Workspace", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n    \"environment\": {\n        \"name\": \"Sample Environment Name (required)\",\n        \"values\": [\n            {\"key\": \"variable_name_1\", \"value\": \"The value of the variable\"},\n            {\"key\": \"variable_name_2\", \"value\": \"\"}\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/environments?workspace={{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments"], "query": [{"key": "workspace", "value": "{{workspace_id}}", "description": "The workspace ID in which the environment will be created"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"environment\": {\n        \"id\": \"f158266e-306b-4702-a2b9-e4ede7878b7a\",\n        \"name\": \"Sample Environment Name (required)\",\n        \"uid\": \"5665-f158266e-306b-4702-a2b9-e4ede7878b7a\"\n    }\n}"}]}, {"name": "Update Environment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n    \"environment\": {\n        \"name\": \"New Name\",\n        \"values\": [\n            {\"key\": \"name\", \"value\": \"<PERSON>\"},\n            {\"key\": \"occupation\", \"value\": \"Vigilante\"}\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}, "description": "This endpoint replaces an existing environment.\n\nA sample body is added to the request that conforms to the following JSON schema:\n\n```json\n{\n\t\"type\": \"object\",\n\t\"properties\": {\n\t\t\"environment\": {\n\t\t\t\"type\": \"object\",\n\t\t\t\"properties\": {\n\t\t\t\t\"name\": {\n\t\t\t\t\t\"type\": \"string\",\n\t\t\t\t\t\"maxLength\": 254,\n\t\t\t\t\t\"minLength\": 1\n\t\t\t\t},\n\t\t\t\t\"values\": {\n\t\t\t\t\t\"type\": \"array\",\n\t\t\t\t\t\"maxItems\": 100,\n\t\t\t\t\t\"additionalItems\": false,\n\t\t\t\t\t\"items\": {\n\t\t\t\t\t\t\"type\": \"object\",\n\t\t\t\t\t\t\"properties\": {\n\t\t\t\t\t\t\t\"key\": {\n\t\t\t\t\t\t\t\t\"type\": \"string\",\n\t\t\t\t\t\t\t\t\"maxLength\": 254,\n\t\t\t\t\t\t\t\t\"minLength\": 1\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"value\": {\n\t\t\t\t\t\t\t\t\"type\": \"string\",\n\t\t\t\t\t\t\t\t\"maxLength\": 254,\n\t\t\t\t\t\t\t\t\"minLength\": 1\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"type\": {\n\t\t\t\t\t\t\t\t\"type\": \"string\"\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\"enabled\": {\n\t\t\t\t\t\t\t\t\"type\": \"boolean\"\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\t\"required\": [\n\t\t\t\t\t\t\t\"key\",\n\t\t\t\t\t\t\t\"value\"\n\t\t\t\t\t\t]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t\"required\": [\n\t\t\"environment\"\n\t]\n}\n```\n\nOn successful updation of the environment, the API returns the environment name and `id`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Failure Response", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "warning": ""}, {"key": "X-Api-Key", "value": "{{postman_api_key}}", "warning": ""}], "body": {"mode": "raw", "raw": "{\n    \"environment\": {\n        \"name\": \"New Name\",\n        \"values\": [\n            null,\n            {\"key\": \"occupation\", \"value\": \"Vigilante\"}\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "115", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Thu, 23 Jun 2016 22:39:35 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"73-rsdRzSKmRbKsO0LZ/NHoag\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1466721624", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"malformedRequestError\",\"message\":\"Invalid type: null (expected object) at environment.values.0\"}}"}, {"name": "Successful Response", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n    \"environment\": {\n        \"name\": \"New Name\",\n        \"values\": [\n            {\"key\": \"name\", \"value\": \"<PERSON>\"},\n            {\"key\": \"occupation\", \"value\": \"Vigilante\"}\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "110", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Thu, 23 Jun 2016 22:33:25 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"83-OejlQB33FVmPE4Eq7sgMSw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1466721229", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"environment\":{\"id\":\"357668d2-84f1-2264-438b-113095359f80\",\"name\":\"New Name\",\"uid\":\"631643-357668d2-84f1-2264-438b-113095359f80\"}}"}]}, {"name": "Delete Environment", "request": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}, "description": "This endpoint allows you to delete a single environment based on an environment's unique `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Environment Not Found", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "103", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 11:00:21 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"60-Hq9sfZ7dwrmtkpUBnjfOQA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479898881", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"error\":{\"name\":\"instanceNotFoundError\",\"message\":\"The specified environment does not exist.\"}}"}, {"name": "Environment Deleted", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/environments/{{environment_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["environments", "{{environment_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "93", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 23 Nov 2016 10:58:03 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"6f-Ffate/KmwA1dyniRhI/OyQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "53", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1479898715", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"environment\":{\"id\":\"4dfb28a4-9a6c-4ce4-b31a-17c26a8b2cce\",\"uid\":\"5852-4dfb28a4-9a6c-4ce4-b31a-17c26a8b2cce\"}}"}]}], "protocolProfileBehavior": {}}, {"name": "<PERSON><PERSON>", "item": [{"name": "All Mocks", "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/mocks", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks"]}, "description": "This endpoint fetches all the mocks that you have created.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/mocks", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "264", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Mon, 06 Mar 2017 15:38:49 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"238-xO+SM8MMUFSXisoiufApyQ\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.10.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}], "cookie": [], "body": "{\"mocks\":[{\"id\":\"0fca2246-c108-41f5-8454-cc032def329f\",\"owner\":\"1679925\",\"uid\":\"1679925-0fca2246-c108-41f5-8454-cc032def329f\",\"collection\":\"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\"environment\":\"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\",\"mockUrl\":\"https://0fca2246-c108-41f5-8454-cc032def329f.mock.pstmn.io\"},{\"id\":\"2c624389-705a-4e66-9777-05314b431796\",\"owner\":\"1679925\",\"uid\":\"1679925-2c624389-705a-4e66-9777-05314b431796\",\"collection\":\"1679925-37294bb0-e27b-5e52-93ae-c07dd445216d\",\"mockUrl\":\"https://2c624389-705a-4e66-9777-05314b431796.mock.pstmn.io\"}]}"}]}, {"name": "Single Mock", "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/mocks/{{mock_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks", "{{mock_uid}}"]}, "description": "This endpoint fetches you the basic information about a single mock using its `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/mocks/{{mock_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks", "{{mock_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "197", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Mon, 06 Mar 2017 15:37:16 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"13d-ZYjjs3SUBAYM3G91RE5yBw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.10.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}], "cookie": [], "body": "{\"mock\":{\"id\":\"0fca2246-c108-41f5-8454-cc032def329f\",\"owner\":\"1679925\",\"uid\":\"1679925-0fca2246-c108-41f5-8454-cc032def329f\",\"collection\":\"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\"environment\":\"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\",\"mockUrl\":\"https://0fca2246-c108-41f5-8454-cc032def329f.mock.pstmn.io\"}}"}]}, {"name": "Create <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"mock\": {\n\t\t\"collection\": \"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\n\t\t\"environment\": \"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\"\n\t}\n}", "options": {}}, "url": {"raw": "https://api.getpostman.com/mocks", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks"]}, "description": "This endpoint allows you to create a mock on a collection. You can also provide an environment UID to resolve any environment variables in the collection.\n\nYou can also specify the context of a workspace to create a mock in directly by passing the `workspace` as a query param.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"mock\": {\n\t\t\"collection\": \"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\n\t\t\"environment\": \"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/mocks", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": "Indicates whether or not the response to the request can be exposed when the credentials flag is true. When used as part of a response to a preflight request, this indicates whether or not the actual request can be made using credentials."}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": "Used in response to a preflight request to indicate which HTTP headers can be used when making the actual request."}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": "Specifies the method or methods allowed when accessing the resource. This is used in response to a preflight request."}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": "Lets a server whitelist headers that browsers are allowed to access."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": "The type of encoding used on the data."}, {"key": "Content-Length", "value": "197", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "Mon, 06 Mar 2017 15:36:31 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "ETag", "value": "W/\"13d-ZYjjs3SUBAYM3G91RE5yBw\"", "name": "ETag", "description": "An identifier for a specific version of a resource, often a message digest"}, {"key": "Server", "value": "nginx/1.10.1", "name": "Server", "description": "A name for the server"}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": "Tells downstream proxies how to match future request headers to decide whether the cached response can be used rather than requesting a fresh one from the origin server."}], "cookie": [], "body": "{\"mock\":{\"id\":\"0fca2246-c108-41f5-8454-cc032def329f\",\"owner\":\"1679925\",\"uid\":\"1679925-0fca2246-c108-41f5-8454-cc032def329f\",\"collection\":\"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\"environment\":\"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\",\"mockUrl\":\"https://0fca2246-c108-41f5-8454-cc032def329f.mock.pstmn.io\"}}"}, {"name": "Create Mock in a Workspace", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"mock\": {\n    \"collection\": \"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\n    \"environment\": \"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\"\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/mocks?workspace={{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks"], "query": [{"key": "workspace", "value": "{{workspace_id}}", "description": "The workspace ID in which the mock will be created"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"mock\": {\n        \"id\": \"0fca2246-c108-41f5-8454-cc032def329f\",\n        \"owner\": \"1679925\",\n        \"uid\": \"1679925-0fca2246-c108-41f5-8454-cc032def329f\",\n        \"collection\": \"1679925-39fee52f-b806-3ffa-1173-00a6f5b183dc\",\n        \"environment\": \"1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524\",\n        \"mockUrl\": \"https://0fca2246-c108-41f5-8454-cc032def329f.mock.pstmn.io\"\n    }\n}"}]}, {"name": "Delete Mock", "request": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "", "options": {}}, "url": {"raw": "https://api.getpostman.com/mocks/{{mock_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks", "{{mock_uid}}"]}, "description": "This endpoint allows you to delete an existing mock using its `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.getpostman.com/mocks/{{mock_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["mocks", "{{mock_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "89", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Mon, 06 Mar 2017 15:39:40 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"6b-MQrx/mZrWjtpAjcBfGb69Q\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.10.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}], "cookie": [], "body": "{\"mock\":{\"id\":\"0fca2246-c108-41f5-8454-cc032def329f\",\"uid\":\"1679925-0fca2246-c108-41f5-8454-cc032def329f\"}}"}]}], "protocolProfileBehavior": {}}, {"name": "Monitors", "item": [{"name": "All Monitors", "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}, "description": "The `/monitors` endpoint returns a list of all [monitors](https://monitor.getpostman.com/) that are accessible by you.\n\nThe response contains an array of monitors information containing the `name`, `id`, `owner` and `uid` of each monitor.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "229", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 30 Nov 2016 07:23:18 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"1bf-OdFCFXJK2lsIV7bcwRG0DA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480490657", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "0e9f9b1f3e7218e7fd625cca14597bc771064a433bdd75b5a40196cef86c3fa29d483358e0b254b0a097ac305b132ec8481e2c5b835663fbed3bbb93b5a747cf8776efe85650e29a8e5f0a63906480e5b9e5a9860bef703234812ce9f65ea6dcbc099ac952d1c36338e2d192a793b82a1d8a2dd6dd031f831c08805a9e7ea1f55d46c54cf6ecaecbac84ddf581c8a267a116c0d6d935f19171bce93c6e040c0bd49fc1325bdb8929914548d7c0cae78796aba4073dc9ab63194a90e430919d8757c7ccb7b4dffc61b9cd4a748edc88d0b4ba727c83ea1af546908ff63a1e2ee9784a7b9af2f61997e0dd34adcb479151d11d0e8d67ba76050d55c7bbb88f2f700e14f848f6b929b441869fba53436eb7983660d0e4af82aa0dc5c47dddf974599280179889c03c3e38212eaa8270c641298f0a49b923ed3d04d61e2c6c25844b67df9e4814d378b440e8124e429dc292a147038a57c03e8b8ece700b376fcf8e002dca35142f97b5f49144e2f7477b99d767dff12bb37dd846419051bdaa4c6d931545a5df09bda8a80a336585e5d6ba44f087607512344f91321a359dc150cb411600fbf0d974f63116046d681d3f82a16e50e3bb69c463430e284f64e007f354a9ed4a2afdb27caad0414d451875ec78ea98924e32d8f881eb2c260b348e00baf782", "key": "postman.sid"}], "body": "{\"monitors\":[{\"id\":\"1e6b6c77-2031-42d0-9948-76d7251b2dd1\",\"name\":\"<PERSON> & Sherlock Holmes Monitor\",\"uid\":\"5852-1e6b6c77-2031-42d0-9948-76d7251b2dd1\",\"owner\":\"5852\"},{\"id\":\"1e6b6cb7-f13d-4000-acb7-0695757174a8\",\"name\":\"Team Level Monitor\",\"uid\":\"5886-1e6b6cb7-f13d-4000-acb7-0695757174a8\",\"owner\":\"5886\"},{\"id\":\"1e6b6cc1-c760-48e0-968f-4bfaeeae9af1\",\"name\":\"Postman Echo Monitor\",\"uid\":\"5852-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1\",\"owner\":\"5852\"}]}"}]}, {"name": "Single Monitor", "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}, "description": "This endpoint fetches you basic information about the monitor using its `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "244", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 30 Nov 2016 09:03:14 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"15a-ZTYuAAC/bdMBGSKE4KICVw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480496653", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "0e9f9b1f3e7218e7fd625cca14597bc771064a433bdd75b5a40196cef86c3fa29d483358e0b254b0a097ac305b132ec8481e2c5b835663fbed3bbb93b5a747cf8776efe85650e29a8e5f0a63906480e5b9e5a9860bef703234812ce9f65ea6dcbc099ac952d1c36338e2d192a793b82a1d8a2dd6dd031f831c08805a9e7ea1f55d46c54cf6ecaecbac84ddf581c8a267a116c0d6d935f19171bce93c6e040c0bd49fc1325bdb8929914548d7c0cae78796aba4073dc9ab63194a90e430919d8757c7ccb7b4dffc61b9cd4a748edc88d0b4ba727c83ea1af546908ff63a1e2ee9784a7b9af2f61997e0dd34adcb479151d11d0e8d67ba76050d55c7bbb88f2f700e14f848f6b929b441869fba53436eb7983660d0e4af82aa0dc5c47dddf974599280179889c03c3e38212eaa8270c641298f0a49b923ed3d04d61e2c6c25844b67df9e4814d378b440e8124e429dc292a147038a57c03e8b8ece700b376fcf8e002dca35142f97b5f49144e2f7477b99d767dff12bb37dd846419051bdaa4c6d931545a5df09bda8a80a336585e5d6ba44f087607512344f91321a359dc150cb411600fbf0d974f63116046d681d3f82a16e50e3bb69c463430e284f64e007f354a9ed4a2afdb27caad0414d451875ec78ea98924e32d8f881eb2c260b348e00baf782", "key": "postman.sid"}], "body": "{\n    \"monitor\": {\n        \"id\": \"1e6b6cc1-c760-48e0-968f-4bfaeeae9af1\",\n        \"name\": \"Postman Echo Monitor\",\n        \"uid\": \"5852-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1\",\n        \"owner\": \"5852\",\n        \"collectionUid\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n        \"environmentUid\": \"5851-8d05dd85-222c-1452-553b-e76a531b71ed\",\n        \"options\": {\n            \"strictSSL\": true,\n            \"followRedirects\": true,\n            \"requestTimeout\": null,\n            \"requestDelay\": 0\n        },\n        \"notifications\": {\n        \t\"onError\": [{ \"email\": \"<EMAIL>\" }],\n        \t\"onFailure\": [{ \"email\": \"<EMAIL>\" }]\n        },\n        \"distribution\": [],\n        \"schedule\": {\n            \"cron\": \"0 0 * * * *\",\n            \"timezone\": \"Asia/Calcutta\",\n            \"nextRun\": \"2016-11-30T09:30:00.000Z\"\n        },\n        \"lastRun\": {\n            \"status\": \"failed\",\n            \"startedAt\": \"2020-03-25T15:45:29.218Z\",\n            \"finishedAt\": \"2020-03-25T15:45:31.340Z\",\n            \"stats\": {\n                \"assertions\": {\n                    \"total\": 8,\n                    \"failed\": 1\n                },\n                \"requests\": {\n                    \"total\": 4\n                }\n            }\n        }\n    }\n}"}, {"name": "Monitor Not Found", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "101", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 30 Nov 2016 09:04:00 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"5c-jtix81JynjbtmVVCX/F18A\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480496653", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "0e9f9b1f3e7218e7fd625cca14597bc771064a433bdd75b5a40196cef86c3fa29d483358e0b254b0a097ac305b132ec8481e2c5b835663fbed3bbb93b5a747cf8776efe85650e29a8e5f0a63906480e5b9e5a9860bef703234812ce9f65ea6dcbc099ac952d1c36338e2d192a793b82a1d8a2dd6dd031f831c08805a9e7ea1f55d46c54cf6ecaecbac84ddf581c8a267a116c0d6d935f19171bce93c6e040c0bd49fc1325bdb8929914548d7c0cae78796aba4073dc9ab63194a90e430919d8757c7ccb7b4dffc61b9cd4a748edc88d0b4ba727c83ea1af546908ff63a1e2ee9784a7b9af2f61997e0dd34adcb479151d11d0e8d67ba76050d55c7bbb88f2f700e14f848f6b929b441869fba53436eb7983660d0e4af82aa0dc5c47dddf974599280179889c03c3e38212eaa8270c641298f0a49b923ed3d04d61e2c6c25844b67df9e4814d378b440e8124e429dc292a147038a57c03e8b8ece700b376fcf8e002dca35142f97b5f49144e2f7477b99d767dff12bb37dd846419051bdaa4c6d931545a5df09bda8a80a336585e5d6ba44f087607512344f91321a359dc150cb411600fbf0d974f63116046d681d3f82a16e50e3bb69c463430e284f64e007f354a9ed4a2afdb27caad0414d451875ec78ea98924e32d8f881eb2c260b348e00baf782", "key": "postman.sid"}], "body": "{\"error\":{\"name\":\"instanceNotFoundError\",\"message\":\"The specified monitor does not exist.\"}}"}]}, {"name": "Create Monitor", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"monitor\": {\n    \"name\": \"This is a Monitor Name\",\n    \"schedule\": {\n      \"cron\": \"0 0 * * *\",\n      \"timezone\": \"Asia/Kolkata\"\n    },\n    \"collection\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n    \"environment\": \"5852-8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n  }\n}", "options": {}}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}, "description": "This endpoint allows you to create a monitor.\n\nSome sample `cron` values are:\n\n| Frequency                  | <PERSON>ron <PERSON>   |\n|-----------------------|----------------|\n| Every 5 minutes       | `*/5 * * * *`  |\n| Every 30 minutes     | `*/30 * * * *` |\n| Every Hour         | `0 */1 * * *`  |\n| Every 6 Hours      | `0 */6 * * *`  |\n| Every day at 5pm    | `0 17 * * *`   |\n| Every Monday at 12pm  | `0 12 * * MON` |\n| Every weekday (Monday - Friday) at 6am | `0 6 * * MON-FRI` |\n\nNote: Currently, you can only create monitors at some limited schedules. You can head to [Postman Monitors](https://monitor.getpostman.com) to see the allowed schedules. \n\nFor more information about the format of the `timezone` value, check this [list of time zones.](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)\n\nYou can also specify the context of a workspace to create a monitor in directly by passing the `workspace` as a query param.\n\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Invalid Collection UID", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"monitor\": {\n\t\t\"name\": \"This is a Monitor Name\",\n\t\t\"schedule\": {\n\t\t\t\"cron\": \"0 0 * * *\",\n\t\t\t\"timezone\": \"Asia/Kolkata\"\n\t\t},\n\t\t\"collection\": \"8d05dd85-222c-1452-553b-e76a531b71ed\",\n\t\t\"environment\": \"5852-8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "156", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 02 Dec 2016 13:41:53 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"9c-+1K5xM5f5LlR7UsFU/yO5w\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480686136", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"error\":{\"name\":\"invalidUidError\",\"message\":\"The specified uid is invalid.\",\"details\":{\"param\":\"collection\",\"uid\":\"8d05dd85-222c-1452-553b-e76a531b71ed\"}}}"}, {"name": "Invalid Timezone", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"monitor\": {\n\t\t\"name\": \"This is a Monitor Name\",\n\t\t\"schedule\": {\n\t\t\t\"cron\": \"0 0 * * *\",\n\t\t\t\"timezone\": \"this is not a timezone\"\n\t\t},\n\t\t\"collection\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n\t\t\"environment\": \"5852-8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "126", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 02 Dec 2016 13:40:47 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"7e-gWN9mOkgiTpGZ2B6SIi6iA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480686056", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"error\":{\"name\":\"invalidParamsError\",\"message\":\"The request had invalid parameters\",\"details\":{\"param\":\"schedule.timezone\"}}}"}, {"name": "Create Monitor in a Workspace", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"monitor\": {\n    \"name\": \"This is a Monitor Name\",\n    \"schedule\": {\n      \"cron\": \"0 0 * * *\",\n      \"timezone\": \"Asia/Kolkata\"\n    },\n    \"collection\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n    \"environment\": \"5852-8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/monitors?workspace={{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"], "query": [{"key": "workspace", "value": "{{workspace_id}}", "description": "The workspace ID in which the monitor will be created"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "name": "Content-Type", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"monitor\": {\n        \"id\": \"1e6b6dfd-7ba4-4590-9ee1-5948102d7797\",\n        \"name\": \"Monitor Name\",\n        \"uid\": \"5852-1e6b6dfd-7ba4-4590-9ee1-5948102d7797\"\n    }\n}"}, {"name": "<PERSON><PERSON> Not Allowed", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"monitor\": {\n\t\t\"name\": \"This is a Monitor Name\",\n\t\t\"schedule\": {\n\t\t\t\"cron\": \"* * * * *\",\n\t\t\t\"timezone\": \"Asia/Kolkata\"\n\t\t},\n\t\t\"collection\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n\t\t\"environment\": \"5852-8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "207", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 02 Dec 2016 13:39:56 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"cf-+UkTBd7R+GNsxbJLqnd2PA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480686056", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"error\":{\"name\":\"cronPatternNotAllowedError\",\"message\":\"The specified cron pattern is not allowed. Please check https://monitor.getpostman.com for the allowed schedules.\",\"details\":{\"pattern\":\"* * * * *\"}}}"}, {"name": "Parameter Missing", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\n}"}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "117", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 02 Dec 2016 13:38:55 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"75-U62ZQzAm6WYGiMiI60DXbw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480685995", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"error\":{\"name\":\"paramMissingError\",\"message\":\"Parameter is missing in the request.\",\"details\":{\"param\":\"monitor\"}}}"}, {"name": "Monitor Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "warning": ""}, {"key": "X-Api-Key", "value": "{{postman_api_key}}", "type": "text", "warning": ""}], "body": {"mode": "raw", "raw": "{\n  \"monitor\": {\n    \"name\": \"This is a Monitor Name\",\n    \"schedule\": {\n      \"cron\": \"0 0 * * *\",\n      \"timezone\": \"Asia/Kolkata\"\n    },\n    \"collection\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n    \"environment\": \"5852-8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "104", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 30 Nov 2016 09:31:59 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"81-KfmiUG/YVW/NgIePUM+cqg\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480498379", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "0e9f9b1f3e7218e7fd625cca14597bc771064a433bdd75b5a40196cef86c3fa29d483358e0b254b0a097ac305b132ec8481e2c5b835663fbed3bbb93b5a747cf8776efe85650e29a8e5f0a63906480e5b9e5a9860bef703234812ce9f65ea6dcbc099ac952d1c36338e2d192a793b82a1d8a2dd6dd031f831c08805a9e7ea1f55d46c54cf6ecaecbac84ddf581c8a267a116c0d6d935f19171bce93c6e040c0bd49fc1325bdb8929914548d7c0cae78796aba4073dc9ab63194a90e430919d8757c7ccb7b4dffc61b9cd4a748edc88d0b4ba727c83ea1af546908ff63a1e2ee9784a7b9af2f61997e0dd34adcb479151d11d0e8d67ba76050d55c7bbb88f2f700e14f848f6b929b441869fba53436eb7983660d0e4af82aa0dc5c47dddf974599280179889c03c3e38212eaa8270c641298f0a49b923ed3d04d61e2c6c25844b67df9e4814d378b440e8124e429dc292a147038a57c03e8b8ece700b376fcf8e002dca35142f97b5f49144e2f7477b99d767dff12bb37dd846419051bdaa4c6d931545a5df09bda8a80a336585e5d6ba44f087607512344f91321a359dc150cb411600fbf0d974f63116046d681d3f82a16e50e3bb69c463430e284f64e007f354a9ed4a2afdb27caad0414d451875ec78ea98924e32d8f881eb2c260b348e00baf782", "key": "postman.sid"}], "body": "{\"monitor\":{\"id\":\"1e6b6dfd-7ba4-4590-9ee1-5948102d7797\",\"name\":\"Monitor Name\",\"uid\":\"5852-1e6b6dfd-7ba4-4590-9ee1-5948102d7797\"}}"}, {"name": "Invalid Environment UID", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"monitor\": {\n\t\t\"name\": \"This is a Monitor Name\",\n\t\t\"schedule\": {\n\t\t\t\"cron\": \"0 0 * * *\",\n\t\t\t\"timezone\": \"Asia/Kolkata\"\n\t\t},\n\t\t\"collection\": \"5852-8d05dd85-222c-1452-553b-e76a531b71ed\",\n\t\t\"environment\": \"8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/monitors", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Length", "value": "157", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 02 Dec 2016 13:42:18 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"9d-JjvHmcf5dHMbHMd2MRrpLw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480686198", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"error\":{\"name\":\"invalidUidError\",\"message\":\"The specified uid is invalid.\",\"details\":{\"param\":\"environment\",\"uid\":\"8a4c21ea-2a36-40d1-a82d-d717fac9d6ef\"}}}"}]}, {"name": "Update Monitor", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n  \"monitor\": {\n    \"name\": \"Updated Monitor Name\",\n    \"schedule\": {\n      \"cron\": \"*/5 * * * *\",\n      \"timezone\": \"America/Chicago\"\n    }\n  }\n}", "options": {}}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}, "description": "This endpoint allows you to update a monitor using its `uid`. Only the monitor name and its schedule can be updated.\n\nSome example `cron` values are:\n\n| Frequency                  | <PERSON>ron Pat<PERSON>   |\n|-----------------------|----------------|\n| Every 5 minutes       | `*/5 * * * *`  |\n| Every 30 minutes     | `*/30 * * * *` |\n| Every Hour         | `0 */1 * * *`  |\n| Every 6 Hours      | `0 */6 * * *`  |\n| Every day at 5pm    | `0 17 * * *`   |\n| Every Monday at 12pm  | `0 12 * * MON` |\n| Every weekday (Monday - Friday) at 6am | `0 6 * * MON-FRI` |\n\nNote: Currently, you can only create monitors at some limited schedules. You can head to [Postman Monitors](https://monitor.getpostman.com) to see the allowed schedules. \n\nFor more information about the format of the `timezone` value, check this [list of time zones.](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones)\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Monitor Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "warning": ""}, {"key": "X-Api-Key", "value": "{{postman_api_key}}", "type": "text", "warning": ""}], "body": {"mode": "raw", "raw": "{\n\t\"monitor\": {\n\t\t\"name\": \"Updated Monitor Name\",\n\t\t\"schedule\": {\n\t\t\t\"cron\": \"*/5 * * * *\",\n\t\t\t\"timezone\": \"America/Chicago\"\n\t\t}\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "112", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Wed, 30 Nov 2016 09:52:30 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"89-lIaqrvUq4zgI6ccMVU0XRw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480499610", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "0e9f9b1f3e7218e7fd625cca14597bc771064a433bdd75b5a40196cef86c3fa29d483358e0b254b0a097ac305b132ec8481e2c5b835663fbed3bbb93b5a747cf8776efe85650e29a8e5f0a63906480e5b9e5a9860bef703234812ce9f65ea6dcbc099ac952d1c36338e2d192a793b82a1d8a2dd6dd031f831c08805a9e7ea1f55d46c54cf6ecaecbac84ddf581c8a267a116c0d6d935f19171bce93c6e040c0bd49fc1325bdb8929914548d7c0cae78796aba4073dc9ab63194a90e430919d8757c7ccb7b4dffc61b9cd4a748edc88d0b4ba727c83ea1af546908ff63a1e2ee9784a7b9af2f61997e0dd34adcb479151d11d0e8d67ba76050d55c7bbb88f2f700e14f848f6b929b441869fba53436eb7983660d0e4af82aa0dc5c47dddf974599280179889c03c3e38212eaa8270c641298f0a49b923ed3d04d61e2c6c25844b67df9e4814d378b440e8124e429dc292a147038a57c03e8b8ece700b376fcf8e002dca35142f97b5f49144e2f7477b99d767dff12bb37dd846419051bdaa4c6d931545a5df09bda8a80a336585e5d6ba44f087607512344f91321a359dc150cb411600fbf0d974f63116046d681d3f82a16e50e3bb69c463430e284f64e007f354a9ed4a2afdb27caad0414d451875ec78ea98924e32d8f881eb2c260b348e00baf782", "key": "postman.sid"}], "body": "{\"monitor\":{\"id\":\"1e6b6e2a-c2ad-4090-b750-0df4e6624352\",\"name\":\"Updated Monitor Name\",\"uid\":\"5852-1e6b6e2a-c2ad-4090-b750-0df4e6624352\"}}"}]}, {"name": "Delete Monitor", "request": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "", "options": {}}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}, "description": "This endpoint can be used to delete an existing monitor using its `uid`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Monitor Not Found", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "101", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Sun, 04 Dec 2016 14:33:23 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"5c-jtix81JynjbtmVVCX/F18A\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480862063", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"error\":{\"name\":\"instanceNotFoundError\",\"message\":\"The specified monitor does not exist.\"}}"}, {"name": "Monitor Deleted", "originalRequest": {"method": "DELETE", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "89", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 02 Dec 2016 13:44:44 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"6b-czWJMJECLwheqb5U51YcRw\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "58", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480686329", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"monitor\":{\"id\":\"1e6b8957-35f9-42a0-8d2f-f03d7085b3d2\",\"uid\":\"5852-1e6b8957-35f9-42a0-8d2f-f03d7085b3d2\"}}"}]}, {"name": "Run a Monitor", "request": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}/run", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}", "run"]}, "description": "This endpoint will run the monitor instantly and wait for the monitor to run completely. It responds with the run results.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Monitor Run with No Errors", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}/run", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}", "run"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "764", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Sun, 04 Dec 2016 14:29:40 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"8c6-6zrn1CQLRxAmfbdTUZAVQg\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "10", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "9", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480861788", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"run\":{\"info\":{\"jobId\":\"1e6ba2e1-6256-4430-bafa-a06e591523bd\",\"monitorId\":\"1e6b8970-fd13-4480-b011-b3b3e3cd271d\",\"name\":\"Sample Collection monitor 1 #54\",\"collectionUid\":\"5852-1d3daef4-2037-4584-ab86-bafd8c8f8a55\",\"status\":\"success\",\"startedAt\":\"2016-12-04T14:29:39.000Z\",\"finishedAt\":\"2016-12-04T14:29:39.000Z\"},\"stats\":{\"assertions\":{\"total\":1,\"failed\":0},\"requests\":{\"total\":3,\"failed\":0}},\"executions\":[{\"id\":1,\"item\":{\"id\":\"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\"name\":\"Sample POST Request\"},\"request\":{\"method\":\"POST\",\"url\":\"echo.getpostman.com/post\",\"headers\":{\"content-type\":\"application/json\",\"accept\":\"*/*\",\"accept-encoding\":\"gzip, deflate\",\"content-length\":18},\"body\":{\"contentLength\":18,\"mode\":\"raw\"},\"timestamp\":\"2016-12-04T14:29:39.520Z\"},\"response\":{\"code\":200,\"body\":{\"contentLength\":298},\"responseTime\":57,\"responseSize\":298,\"headers\":{\"date\":\"Sun, 04 Dec 2016 14:29:39 GMT\",\"content-type\":\"application/json; charset=utf-8\",\"transfer-encoding\":\"chunked\",\"connection\":\"keep-alive\",\"content-encoding\":\"gzip\"}}},{\"id\":2,\"item\":{\"id\":\"f790d046-755d-44f5-a416-b825e18dfd9d\",\"name\":\"Sample GET Request\"},\"request\":{\"method\":\"GET\",\"url\":\"echo.getpostman.com/get\",\"headers\":{\"accept\":\"*/*\",\"accept-encoding\":\"gzip, deflate\"},\"body\":{\"contentLength\":0,\"mode\":\"formdata\"},\"timestamp\":\"2016-12-04T14:29:39.586Z\"},\"response\":{\"code\":200,\"body\":{\"contentLength\":282},\"responseTime\":46,\"responseSize\":282,\"headers\":{\"date\":\"Sun, 04 Dec 2016 14:29:39 GMT\",\"content-type\":\"application/json; charset=utf-8\",\"transfer-encoding\":\"chunked\",\"connection\":\"keep-alive\",\"content-encoding\":\"gzip\"}},\"assertions\":{\"Status code is 200\":true}},{\"id\":3,\"item\":{\"id\":\"336e6e17-6d3e-4b8f-a48f-b7e75cf13afb\",\"name\":\"This is the second request\"},\"request\":{\"method\":\"POST\",\"url\":\"echo.getpostman.com/post\",\"headers\":{\"accept\":\"*/*\",\"accept-encoding\":\"gzip, deflate\",\"content-length\":\"\"},\"body\":{\"contentLength\":0,\"mode\":\"formdata\"},\"timestamp\":\"2016-12-04T14:29:39.965Z\"},\"response\":{\"code\":200,\"body\":{\"contentLength\":347},\"responseTime\":10,\"responseSize\":347,\"headers\":{\"date\":\"Sun, 04 Dec 2016 14:29:39 GMT\",\"content-type\":\"application/json; charset=utf-8\",\"transfer-encoding\":\"chunked\",\"connection\":\"keep-alive\",\"content-encoding\":\"gzip\"}}}],\"failures\":[]}}"}, {"name": "Monitor Run with Errors", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/monitors/{{monitor_uid}}/run", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["monitors", "{{monitor_uid}}", "run"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "820", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Sun, 04 Dec 2016 14:30:26 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"94e-AsVVD4NpEpSicuFS4hKbgA\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.0", "name": "Server", "description": ""}, {"key": "X-RateLimit-Limit", "value": "10", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "9", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1480861833", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [{"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "yes", "key": "getpostmanlogin"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "813af79fd620c970d59f4e41275292a96873bb5065975eccf67a84cfdc24b448037430d26c2a29f4192ad492875ed7a0d53bbea8c63f760bb75c886624b4da560fc52c7f9f9e79837bee212f90b9a59c1dfcf94b95156b20f45169eab2ed93b0c38669819143ae41e45dfaac6a5f0cf6d5e76ca40134c3bd9ef9f5532ad6d179519aa0315fe4e6e157ee9d71c9f14aa9b0455ff84f94ae7519f1139d0cccdeeef7e2aafe1696cf640ec874c62d4d5bda3dbe17cf6083fb276b7f3355eb19162430f171166d19a7d79da028c76b3c2b97e88c93dc7183bc486d9d331b93b9a20de1216e84ca74f7b6887df61785d0b65be19f56283523f211fd43a772afa39aadc3b8dc011ba680b69a7e4fcda83be75d593c8ce6c6997bf530b57a8036881ea3d5b3c28d096099ca712f5d6916b453f1b338c455c4f861da638dffd05fd2a3dd5b9dd6dbe5f2c3a30e304363c9c2bcfbe5d9abeba9c06df16d155a70c6b0a9848f7446a5646e58640d7fad642df11350f528e4bacb5c446986cddf8f0c7ad10ec1067daccca962d6fb90aad8db55c2ffa1dc8716865350d5127cb69e44cfc4511836cbc5828c1c01370c7464fef1f07c716456a57a467bc1445ea88b705ef9c0c068948a73dca851804663befbc2d9aec7cfc3a2daf377b1a8c020091216d82504ff9b38c84234ad9b6f8c596a", "key": "postman.sid"}, {"expires": "Invalid Date", "httpOnly": false, "domain": "getpostman.com", "path": "/", "secure": false, "value": "b8ee90dd180562a5a21edad92ea0b987583b7599811884947b9aae88d0c060e8abb0c337534285da6defa4cfabba320b37b611b2c9586b81cb38220830b58440a2ff03756c898b89eb08799bc486c7dc08643ef1ee6e38e5988c4aafda47f1ed26c4218a23620f43d57aa19bb7f79762b394384215a7805e6b1c2cc092b6ac5ad8e8848b1ce655edba6f52820a2ac5acf3d289dda878c6a1fe62e88bb4159f605e8081afa2afce7a72a0c5d96515d566bcbf9cb3842cfc2b7f3a48badbcbfb9e5907b6451674d2c09cf6bf0b34b967edfc3a6389a834f7be21592ba2fbf7aa9eaaa6b87f99590813ded37c6e4ad17e7afcca74455b703d2d653e6ce063ce8eb195ce46c910a1b61fb71f13a8cb52ab2a7bded1b9b5a6b56ad5a66ceafc20afc70962a1f49b8abf2ce3f9e7cf3f44aaf0f41f81605b4ee612f3044d81b787a7cb128c56474a25cdc13803e9057603ead1f1fd6666ae5467ca9e250bb228dfd11dafd16345019bb7e06c58b27bf69d550cbfcef348bc746fdea61d4ea93c48d5f5e4e729adf18655669170f0a059025a79fb2a48f924d9748895a42780d1a9fae32ebf2d57df434c60b675412e217d126b9edbd28b949dfcbcbc8518aa51339d3e1d1f37c6cac4866064c54a476cf94d749a570938a7ad3ce28a9f1c5ea6623b", "key": "postman-staging.sid"}], "body": "{\"run\":{\"info\":{\"jobId\":\"1e6ba2e3-1aaf-4c10-bd5f-905943284b2a\",\"monitorId\":\"1e6b8970-fd13-4480-b011-b3b3e3cd271d\",\"name\":\"Sample Collection monitor 1 #56\",\"collectionUid\":\"5852-1d3daef4-2037-4584-ab86-bafd8c8f8a55\",\"status\":\"failed\",\"startedAt\":\"2016-12-04T14:30:25.000Z\",\"finishedAt\":\"2016-12-04T14:30:26.000Z\"},\"stats\":{\"assertions\":{\"total\":1,\"failed\":1},\"requests\":{\"total\":3,\"failed\":1}},\"executions\":[{\"id\":1,\"item\":{\"id\":\"b5e8d7dd-909c-4ba7-aef4-8609bc50b586\",\"name\":\"<PERSON>ple POST Request\"},\"request\":{\"method\":\"POST\",\"url\":\"echo.getpostman.com/post\",\"headers\":{\"content-type\":\"application/json\",\"accept\":\"*/*\",\"accept-encoding\":\"gzip, deflate\",\"content-length\":18},\"body\":{\"contentLength\":18,\"mode\":\"raw\"},\"timestamp\":\"2016-12-04T14:30:26.025Z\"},\"response\":{\"code\":200,\"body\":{\"contentLength\":298},\"responseTime\":26,\"responseSize\":298,\"headers\":{\"date\":\"Sun, 04 Dec 2016 14:30:26 GMT\",\"content-type\":\"application/json; charset=utf-8\",\"transfer-encoding\":\"chunked\",\"connection\":\"keep-alive\",\"content-encoding\":\"gzip\"}}},{\"id\":2,\"item\":{\"id\":\"f790d046-755d-44f5-a416-b825e18dfd9d\",\"name\":\"Sample GET Request\"},\"request\":{\"method\":\"GET\",\"url\":\"echo.getpostman.com/get\",\"headers\":{\"accept\":\"*/*\",\"accept-encoding\":\"gzip, deflate\"},\"body\":{\"contentLength\":0,\"mode\":\"formdata\"},\"timestamp\":\"2016-12-04T14:30:26.093Z\"},\"response\":{\"code\":200,\"body\":{\"contentLength\":280},\"responseTime\":46,\"responseSize\":280,\"headers\":{\"date\":\"Sun, 04 Dec 2016 14:30:26 GMT\",\"content-type\":\"application/json; charset=utf-8\",\"transfer-encoding\":\"chunked\",\"connection\":\"keep-alive\",\"content-encoding\":\"gzip\"}},\"assertions\":{\"Status code is 400\":false}},{\"id\":3,\"item\":{\"id\":\"336e6e17-6d3e-4b8f-a48f-b7e75cf13afb\",\"name\":\"This is the second request\"},\"request\":{\"method\":\"POST\",\"url\":\"echo.getpostman.com/post\",\"headers\":{\"accept\":\"*/*\",\"accept-encoding\":\"gzip, deflate\",\"content-length\":\"\"},\"body\":{\"contentLength\":0,\"mode\":\"formdata\"},\"timestamp\":\"2016-12-04T14:30:26.477Z\"},\"response\":{\"code\":200,\"body\":{\"contentLength\":345},\"responseTime\":9,\"responseSize\":345,\"headers\":{\"date\":\"Sun, 04 Dec 2016 14:30:26 GMT\",\"content-type\":\"application/json; charset=utf-8\",\"transfer-encoding\":\"chunked\",\"connection\":\"keep-alive\",\"content-encoding\":\"gzip\"}}}],\"failures\":[{\"executionId\":2,\"name\":\"AssertionError\",\"message\":\"Expected 'Status code is 400' to be truthy\",\"assertion\":{\"Status code is 400\":false}}]}}"}]}], "protocolProfileBehavior": {}}, {"name": "Workspaces", "item": [{"name": "All workspaces", "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/workspaces", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces"]}, "description": "The `/workspaces` endpoint returns a list of all [workspaces](https://www.getpostman.com/docs/v6/postman/workspaces/intro_to_workspaces) that is accessible by you. The list includes your own workspaces and the workspaces that you have access to.\n\nThe response contains an array of collection information containing the `name`, `id`, and `type` of each workspace.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/workspaces", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"workspaces\": [\n        {\n            \"id\": \"4e6d34c2-cfdb-4b33-8868-12a875bebda3\",\n            \"name\": \"My Workspace\",\n            \"type\": \"personal\"\n        },\n        {\n            \"id\": \"f8801e9e-03a4-4c7b-b31e-5db5cd771696\",\n            \"name\": \"Team workspace\",\n            \"type\": \"team\"\n        }\n    ]\n}"}]}, {"name": "Single workspace", "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}, "description": "Access the contents of a workspace that is accessible to you using its id (`id`). Includes the collections, environments, mocks and monitors of that workspace.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Workspace Not Found", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*", "name": "Access-Control-Allow-Origin", "description": "Specifies a URI that may access the resource. For requests without credentials, the server may specify '*' as a wildcard, thereby allowing any origin to access the resource."}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": "Options that are desired for the connection"}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": "The type of encoding used on the data."}, {"key": "Content-Length", "value": "106", "name": "Content-Length", "description": "The length of the response body in octets (8-bit bytes)"}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": "The mime type of this content"}, {"key": "Date", "value": "<PERSON><PERSON>, 21 Aug 2018 03:14:06 GMT", "name": "Date", "description": "The date and time that the message was sent"}, {"key": "ETag", "value": "W/\"6e-puIep83SZ86wzpDV0QJjS6yKkAA\"", "name": "ETag", "description": "An identifier for a specific version of a resource, often a message digest"}, {"key": "Server", "value": "nginx", "name": "Server", "description": "A name for the server"}], "cookie": [], "body": "{\n  \"error\": {\n    \"name\": \"instanceNotFoundError\",\n    \"message\": \"Instance not found in the database.\"\n  }\n}"}, {"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"workspace\": {\n        \"id\": \"f8801e9e-03a4-4c7b-b31e-5db5cd771696\",\n        \"name\": \"Demo workspace\",\n        \"type\": \"personal\",\n        \"description\": \"Demos.\",\n        \"collections\": [\n            {\n                \"id\": \"7c31b469-bd43-4411-9283-6d397855ee0e\",\n                \"name\": \"Mock demo - collection\",\n                \"uid\": \"1234-7c31b469-bd43-4411-9283-6d397855ee0e\"\n            },\n            {\n                \"id\": \"356fe068-a0f8-4f31-b34d-d12149eac681\",\n                \"name\": \"Mock demo - response code\",\n                \"uid\": \"1234-356fe068-a0f8-4f31-b34d-d12149eac681\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"423fd955-a9c8-47cd-9ab0-09a6a575c4be\",\n                \"name\": \"Mock demo - CNX\",\n                \"uid\": \"1234-423fd955-a9c8-47cd-9ab0-09a6a575c4be\"\n            },\n            {\n                \"id\": \"24c45c84-5147-4c15-bb9a-c3186b81d3cc\",\n                \"name\": \"Mock Demo - response code\",\n                \"uid\": \"1234-24c45c84-5147-4c15-bb9a-c3186b81d3cc\"\n            }\n        ]\n    }\n}"}]}, {"name": "Create Workspace", "request": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace\",\n        \"type\": \"personal\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8154-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8154-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"cda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"1e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces"]}, "description": "This endpoint allows you to create a workspace and populate it with entities like `collections`, `environments`, `mocks` and `monitors` using their `uid`.\n\nOn successful creation of the workspace, the response returns the workspcae `name` and `id`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace\",\n        \"type\": \"personal\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8154-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8154-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"cda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"1e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"workspace\": {\n        \"id\": \"cfbcd9bf-cc8b-4d6f-b8ef-440a3e49e29f\",\n        \"name\": \"New Workspace\"\n    }\n}"}, {"name": "Instance supplied not found", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace\",\n        \"type\": \"personal\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8153-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8153-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"dda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"2e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Date", "value": "Thu, 15 Nov 2018 06:39:30 GMT"}, {"key": "ETag", "value": "W/\"6e-puIep83SZ86wzpDV0QJjS6yKkAA\""}, {"key": "Server", "value": "nginx"}, {"key": "X-RateLimit-Limit", "value": "60000"}, {"key": "X-RateLimit-Remaining", "value": "59999"}, {"key": "X-RateLimit-Reset", "value": "1542264030"}, {"key": "Content-Length", "value": "110"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"instanceNotFoundError\",\n        \"message\": \"Instance not found in the database.\"\n    }\n}"}]}, {"name": "Update Workspace", "request": {"method": "PUT", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace updated\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8154-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8154-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"cda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"1e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}, "description": "This endpoint allows you to update a workspace and update it's association to entities like `collections`, `environments`, `mocks` and `monitors` using their `uid`.\n\nOn successful updation of the workspace, the response returns the workspcae `name` and `id`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter.\n\n**Note** : The endpoint expects you to send the desired state of the workspace in terms of the associated entities so be careful when trying to update these entities.\n\nFor eg. if your workspace has collections A and B, and in the update call, you send <PERSON>'s uid in the collections array, then A and B would be removed from the workspace and C would be added."}, "response": [{"name": "Not enough permissions", "originalRequest": {"method": "PUT", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace updated\",\n        \"type\": \"personal\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8154-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8154-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"cda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"1e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"forbiddenError\",\n        \"message\": \"You do not have access to update this workspace.\"\n    }\n}"}, {"name": "Specified workspace/entities do not exist", "originalRequest": {"method": "PUT", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace updated\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8154-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8153-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"cda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"1e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Date", "value": "Thu, 15 Nov 2018 06:49:16 GMT"}, {"key": "ETag", "value": "W/\"72-QIgyefaob5yl6DPg/3yHYPd+KT4\""}, {"key": "Server", "value": "nginx"}, {"key": "X-RateLimit-Limit", "value": "60000"}, {"key": "X-RateLimit-Remaining", "value": "59999"}, {"key": "X-RateLimit-Reset", "value": "1542264616"}, {"key": "Content-Length", "value": "114"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"instanceNotFoundError\",\n        \"message\": \"The specified workspace does not exist.\"\n    }\n}"}, {"name": "Successful Response", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"workspace\": {\n        \"name\": \"New Workspace\",\n        \"description\": \"Some description\",\n        \"collections\": [\n            {\n                \"id\": \"e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\",\n                \"name\": \"Straw hats\",\n                \"uid\": \"8154-e1fc3c38-a0b8-44a3-bd44-d753a96b2e9d\"\n            }\n        ],\n        \"environments\": [\n            {\n                \"id\": \"83a1aaa2-a204-4bd8-9b92-4d486918906b\",\n                \"name\": \"env\",\n                \"uid\": \"8154-83a1aaa2-a204-4bd8-9b92-4d486918906b\"\n            }\n        ],\n        \"mocks\": [\n            {\n                \"id\": \"cda672ef-1375-40e9-baee-e20ece8d7b65\"\n            }\n        ],\n        \"monitors\": [\n            {\n                \"id\": \"1e889bd2-3862-4be0-b2c2-9b1fe9673aec\"\n            }\n        ]\n    }\n}"}, "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"workspace\": {\n        \"id\": \"cfbcd9bf-cc8b-4d6f-b8ef-440a3e49e29f\",\n        \"name\": \"New Workspace\"\n    }\n}"}]}, {"name": "Delete Workspace", "request": {"method": "DELETE", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}, "description": "This endpoint allows you to delete an existing workspace.\n\nOn successful deletion of the workspace, the response returns the `id`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Workspace not found", "originalRequest": {"method": "DELETE", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json", "description": "", "type": "text"}], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"instanceNotFoundError\",\n        \"message\": \"The specified workspace does not exist.\"\n    }\n}"}, {"name": "Successful Response", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "https://api.getpostman.com/workspaces/{{workspace_id}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["workspaces", "{{workspace_id}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"workspace\": {\n        \"id\": \"{{workspace_id}}\"\n    }\n}"}]}], "protocolProfileBehavior": {}}, {"name": "User", "item": [{"name": "API Key Owner", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var responseJSON;", "", "// parse response body as JSON", "try { responseJSON = JSON.parse(responseBody); } catch (e) { }", "tests['received a valid response JSON'] = !!responseJSON;", "!responseJSON && (responseJSON = {});", "", "tests['status code is 200'] = (responseCode.code === 200);", "tests['response does not have errors'] = (!responseJSON.error);", "", "tests['response has a user object'] = (typeof responseJSON.user === 'object');", "tests['response user object has id'] = (responseJSON.user && responseJSON.user.id);"], "id": "3cf59aa7-b234-449a-9ec0-9591ce7a5316"}}], "request": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}, "description": "The `/me` endpoint allows you to fetch relevant information pertaining to the API Key being used.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Valid Response", "originalRequest": {"method": "GET", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/me", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["me"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "javascript", "header": [{"key": "Access-Control-Allow-Credentials", "value": "", "name": "Access-Control-Allow-Credentials", "description": ""}, {"key": "Access-Control-Allow-Headers", "value": "", "name": "Access-Control-Allow-Headers", "description": ""}, {"key": "Access-Control-Allow-Methods", "value": "", "name": "Access-Control-Allow-Methods", "description": ""}, {"key": "Access-Control-Allow-Origin", "value": "", "name": "Access-Control-Allow-Origin", "description": ""}, {"key": "Access-Control-Expose-Headers", "value": "", "name": "Access-Control-Expose-Headers", "description": ""}, {"key": "Connection", "value": "keep-alive", "name": "Connection", "description": ""}, {"key": "Content-Encoding", "value": "gzip", "name": "Content-Encoding", "description": ""}, {"key": "Content-Length", "value": "44", "name": "Content-Length", "description": ""}, {"key": "Content-Type", "value": "application/json; charset=utf-8", "name": "Content-Type", "description": ""}, {"key": "Date", "value": "Fri, 27 May 2016 21:24:47 GMT", "name": "Date", "description": ""}, {"key": "ETag", "value": "W/\"18-Cc1VzS/SAG8zo5jZ2MF0/Q\"", "name": "ETag", "description": ""}, {"key": "Server", "value": "nginx/1.8.1", "name": "Server", "description": ""}, {"key": "Vary", "value": "Accept-Encoding", "name": "Vary", "description": ""}, {"key": "X-RateLimit-Limit", "value": "60", "name": "X-RateLimit-Limit", "description": ""}, {"key": "X-RateLimit-Remaining", "value": "59", "name": "X-RateLimit-Remaining", "description": ""}, {"key": "X-RateLimit-Reset", "value": "1464384347", "name": "X-RateLimit-Reset", "description": ""}], "cookie": [], "body": "{\"user\":{\"id\":\"631643\"}}"}]}], "protocolProfileBehavior": {}}, {"name": "Import", "item": [{"name": "Import external API specification", "request": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\" : \"json\",\n  \"input\": {\n    \"openapi\": \"3.0.0\",\n    \"info\": {\n  \t  \"version\": \"1.0.0\",\n  \t  \"title\": \"Swagger Petstore\",\n  \t  \"license\": {\n  \t    \"name\": \"MIT\"\n  \t  }\n    },\n    \"servers\": [\n      {\n        \"url\": \"http://petstore.swagger.io/v1\"\n      }\n    ],\n    \"paths\": {\n  \t  \"/pets\": {\n        \"get\": {\n          \"summary\": \"List all pets\",\n          \"operationId\": \"listPets\",\n          \"parameters\": [\n            {\n              \"name\": \"limit\",\n              \"in\": \"query\",\n              \"description\": \"limit\",\n              \"required\": false,\n              \"schema\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              }\n            }\n          ],\n          \"responses\": {\n            \"default\": {\n              \"description\": \"unexpected error\",\n              \"content\": {\n                \"application/json\": {\n                  \"schema\": {\n                    \"$ref\": \"#/components/schemas/Error\"\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    \"components\": {\n      \"schemas\": {\n        \"Error\": {\n          \"required\": [\n            \"code\",\n            \"message\"\n          ],\n          \"properties\": {\n            \"code\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\"\n            },\n            \"message\": {\n              \"type\": \"string\"\n            }\n          }\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/import/{{importType}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "{{importType}}"]}, "description": "This endpoint allows you to import external API specifications into Postman.\n\nCurrently, this endpoint only supports the <a href=\"https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.2.md\">OpenAPI</a> specification, for which the `importType` will be `openapi`.\n\nOn a successful import, the response will be an array with each element contaning `id`, `name` and `uid` of entities created.\n\nSupported `type`s:\n\n- string\n- json\n- file\n\nThe `input` parameter should be defined based on the `type`.\n\n> To import a file, request body must be form-data with `type` param set to `file`.\n\n**Note**: Refer to examples for different scenarios.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header."}, "response": [{"name": "Success: Import - Stringified body", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"string\",\n  \"input\": \" {\\r\\n    \\\"openapi\\\": \\\"3.0.0\\\",\\r\\n    \\\"info\\\": {\\r\\n  \\t  \\\"version\\\": \\\"1.0.0\\\",\\r\\n  \\t  \\\"title\\\": \\\"Swagger Petstore\\\",\\r\\n  \\t  \\\"license\\\": {\\r\\n  \\t    \\\"name\\\": \\\"MIT\\\"\\r\\n  \\t  }\\r\\n    },\\r\\n    \\\"servers\\\": [\\r\\n      {\\r\\n        \\\"url\\\": \\\"http:\\/\\/petstore.swagger.io\\/v1\\\"\\r\\n      }\\r\\n    ],\\r\\n    \\\"paths\\\": {\\r\\n  \\t  \\\"\\/pets\\\": {\\r\\n        \\\"get\\\": {\\r\\n          \\\"summary\\\": \\\"List all pets\\\",\\r\\n          \\\"operationId\\\": \\\"listPets\\\",\\r\\n          \\\"parameters\\\": [\\r\\n            {\\r\\n              \\\"name\\\": \\\"limit\\\",\\r\\n              \\\"in\\\": \\\"query\\\",\\r\\n              \\\"description\\\": \\\"limit\\\",\\r\\n              \\\"required\\\": false,\\r\\n              \\\"schema\\\": {\\r\\n                \\\"type\\\": \\\"integer\\\",\\r\\n                \\\"format\\\": \\\"int32\\\"\\r\\n              }\\r\\n            }\\r\\n          ],\\r\\n          \\\"responses\\\": {\\r\\n            \\\"default\\\": {\\r\\n              \\\"description\\\": \\\"unexpected error\\\",\\r\\n              \\\"content\\\": {\\r\\n                \\\"application\\/json\\\": {\\r\\n                  \\\"schema\\\": {\\r\\n                    \\\"$ref\\\": \\\"#\\/components\\/schemas\\/Error\\\"\\r\\n                  }\\r\\n                }\\r\\n              }\\r\\n            }\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    },\\r\\n    \\\"components\\\": {\\r\\n      \\\"schemas\\\": {\\r\\n        \\\"Error\\\": {\\r\\n          \\\"required\\\": [\\r\\n            \\\"code\\\",\\r\\n            \\\"message\\\"\\r\\n          ],\\r\\n          \\\"properties\\\": {\\r\\n            \\\"code\\\": {\\r\\n              \\\"type\\\": \\\"integer\\\",\\r\\n              \\\"format\\\": \\\"int32\\\"\\r\\n            },\\r\\n            \\\"message\\\": {\\r\\n              \\\"type\\\": \\\"string\\\"\\r\\n            }\\r\\n          }\\r\\n        }\\r\\n      }\\r\\n    }\\r\\n  }\\r\\n\"\n}"}, "url": {"raw": "https://api.getpostman.com/import/openapi", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "openapi"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"401fcf93-9c45-4832-9616-6c6b698eeda6\",\n            \"name\": \"Swagger Petstore\",\n            \"uid\": \"2282-401fcf93-9c45-4832-9616-6c6b698eeda6\"\n        }\n    ]\n}"}, {"name": "Error: Malformed Request", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\" : \"json\",\n  \"input\": {\n    \"info\": {\n  \t  \"version\": \"1.0.0\",\n  \t  \"title\": \"Swagger Petstore\",\n  \t  \"license\": {\n  \t    \"name\": \"MIT\"\n  \t  }\n    },\n    \"servers\": [\n      {\n        \"url\": \"http://petstore.swagger.io/v1\"\n      }\n    ],\n    \"paths\": {\n  \t  \"/pets\": {\n        \"get\": {\n          \"summary\": \"List all pets\",\n          \"operationId\": \"listPets\",\n          \"parameters\": [\n            {\n              \"name\": \"limit\",\n              \"in\": \"query\",\n              \"description\": \"limit\",\n              \"required\": false,\n              \"schema\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              }\n            }\n          ],\n          \"responses\": {\n            \"default\": {\n              \"description\": \"unexpected error\",\n              \"content\": {\n                \"application/json\": {\n                  \"schema\": {\n                    \"$ref\": \"#/components/schemas/Error\"\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    \"components\": {\n      \"schemas\": {\n        \"Error\": {\n          \"required\": [\n            \"code\",\n            \"message\"\n          ],\n          \"properties\": {\n            \"code\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\"\n            },\n            \"message\": {\n              \"type\": \"string\"\n            }\n          }\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/import/openapi", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "openapi"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"malformedRequestError\",\n        \"message\": \"The specified openapi data is invalid. Please fix the following details as per the spec.\",\n        \"details\": \"Specification must contain a semantic version number of the OAS specification\"\n    }\n}"}, {"name": "Success: Import to a specific workspace with workspace ID passed in the header", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "X-Workspace-Id", "value": "{{workspaceId}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\" : \"json\",\n  \"input\": {\n    \"openapi\": \"3.0.0\",\n    \"info\": {\n  \t  \"version\": \"1.0.0\",\n  \t  \"title\": \"Swagger Petstore\",\n  \t  \"license\": {\n  \t    \"name\": \"MIT\"\n  \t  }\n    },\n    \"servers\": [\n      {\n        \"url\": \"http://petstore.swagger.io/v1\"\n      }\n    ],\n    \"paths\": {\n  \t  \"/pets\": {\n        \"get\": {\n          \"summary\": \"List all pets\",\n          \"operationId\": \"listPets\",\n          \"parameters\": [\n            {\n              \"name\": \"limit\",\n              \"in\": \"query\",\n              \"description\": \"limit\",\n              \"required\": false,\n              \"schema\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              }\n            }\n          ],\n          \"responses\": {\n            \"default\": {\n              \"description\": \"unexpected error\",\n              \"content\": {\n                \"application/json\": {\n                  \"schema\": {\n                    \"$ref\": \"#/components/schemas/Error\"\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    \"components\": {\n      \"schemas\": {\n        \"Error\": {\n          \"required\": [\n            \"code\",\n            \"message\"\n          ],\n          \"properties\": {\n            \"code\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\"\n            },\n            \"message\": {\n              \"type\": \"string\"\n            }\n          }\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/import/openapi", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "openapi"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"e956ff02-7597-4f8d-a5e7-1c18208c9e56\",\n            \"name\": \"Swagger Petstore\",\n            \"uid\": \"2282-e956ff02-7597-4f8d-a5e7-1c18208c9e56\"\n        }\n    ]\n}"}, {"name": "Success: Import to a specific workspace with workspace ID passed as a query parameter", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"type\" : \"json\",\n  \"input\": {\n    \"openapi\": \"3.0.0\",\n    \"info\": {\n  \t  \"version\": \"1.0.0\",\n  \t  \"title\": \"Swagger Petstore\",\n  \t  \"license\": {\n  \t    \"name\": \"MIT\"\n  \t  }\n    },\n    \"servers\": [\n      {\n        \"url\": \"http://petstore.swagger.io/v1\"\n      }\n    ],\n    \"paths\": {\n  \t  \"/pets\": {\n        \"get\": {\n          \"summary\": \"List all pets\",\n          \"operationId\": \"listPets\",\n          \"parameters\": [\n            {\n              \"name\": \"limit\",\n              \"in\": \"query\",\n              \"description\": \"limit\",\n              \"required\": false,\n              \"schema\": {\n                \"type\": \"integer\",\n                \"format\": \"int32\"\n              }\n            }\n          ],\n          \"responses\": {\n            \"default\": {\n              \"description\": \"unexpected error\",\n              \"content\": {\n                \"application/json\": {\n                  \"schema\": {\n                    \"$ref\": \"#/components/schemas/Error\"\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    \"components\": {\n      \"schemas\": {\n        \"Error\": {\n          \"required\": [\n            \"code\",\n            \"message\"\n          ],\n          \"properties\": {\n            \"code\": {\n              \"type\": \"integer\",\n              \"format\": \"int32\"\n            },\n            \"message\": {\n              \"type\": \"string\"\n            }\n          }\n        }\n      }\n    }\n  }\n}"}, "url": {"raw": "https://api.getpostman.com/import/openapi?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "openapi"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"b31be584-1b1e-4444-b581-761edf88fe77\",\n            \"name\": \"Swagger Petstore\",\n            \"uid\": \"2282-b31be584-1b1e-4444-b581-761edf88fe77\"\n        }\n    ]\n}"}, {"name": "Error: <PERSON><PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://api.getpostman.com/import/openapi", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "openapi"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"paramMissingError\",\n        \"message\": \"The request body is missing a value for the type parameter. Check your request and try again.\",\n        \"details\": {\n            \"param\": \"type\"\n        }\n    }\n}"}, {"name": "Success: Import a file", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "file", "type": "text"}, {"key": "input", "description": "A file with the specifications", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/openapi", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "openapi"], "variable": [{"key": "import_type", "value": ""}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"c74f2169-0c59-49f6-b04f-fdec4aba3db3\",\n            \"name\": \"demo\",\n            \"uid\": \"2282-c74f2169-0c59-49f6-b04f-fdec4aba3db3\"\n        },\n        {\n            \"id\": \"49b08197-6b3d-4957-88d3-dbba0e3ee6a0\",\n            \"name\": \"demo\",\n            \"uid\": \"2282-49b08197-6b3d-4957-88d3-dbba0e3ee6a0\"\n        }\n    ]\n}"}]}, {"name": "Import exported data", "request": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "file", "type": "text"}, {"key": "input", "description": "File containing the exported collections or environments", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/exported", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "exported"]}, "description": "This endpoint allows you to import your exported Postman data.\nFor more information about how you can export your data, refer <a href=\"https://go.postman.co/me/export\">Export your Postman data.</a>\n\nOn successful imports, the response will be an array with Each element contaning `id`, `name` and `uid` of entities created.\n\n**Note**: Refer to examples for different scenarios.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header."}, "response": [{"name": "Success: Import the exported file", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "file", "type": "text"}, {"key": "input", "description": "File containing your exported collections and environments", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/exported", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "exported"], "variable": [{"key": "import_type", "value": ""}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"c74f2169-0c59-49f6-b04f-fdec4aba3db3\",\n            \"name\": \"demo\",\n            \"uid\": \"2282-c74f2169-0c59-49f6-b04f-fdec4aba3db3\"\n        },\n        {\n            \"id\": \"49b08197-6b3d-4957-88d3-dbba0e3ee6a0\",\n            \"name\": \"demo\",\n            \"uid\": \"2282-49b08197-6b3d-4957-88d3-dbba0e3ee6a0\"\n        }\n    ]\n}"}, {"name": "Success: Import to a specific workspace with workspace ID passed as a query parameter", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "file", "type": "text"}, {"key": "input", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/exported?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "exported"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"b31be584-1b1e-4444-b581-761edf88fe77\",\n            \"name\": \"Swagger Petstore\",\n            \"uid\": \"2282-b31be584-1b1e-4444-b581-761edf88fe77\"\n        }\n    ]\n}"}, {"name": "Error: <PERSON><PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "input", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/exported", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "exported"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"paramMissingError\",\n        \"message\": \"The request body is missing a value for the type parameter. Check your request and try again.\",\n        \"details\": {\n            \"param\": \"type\"\n        }\n    }\n}"}, {"name": "Error: <PERSON><PERSON><PERSON>", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "invalid_type", "type": "text"}, {"key": "input", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/exported", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "exported"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"error\": {\n        \"name\": \"invalidParamsError\",\n        \"message\": \"The request body has invalid values for the type parameter. Value must be one of file, string, json\",\n        \"details\": {\n            \"param\": \"type\"\n        }\n    }\n}"}, {"name": "Success: Import to a specific workspace with workspace ID passed in the headers", "originalRequest": {"method": "POST", "header": [{"key": "X-Api-Key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "X-Workspace-Id", "value": "{{workspaceId}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-api-key", "value": "{{postman_api_key}}"}], "body": {"mode": "formdata", "formdata": [{"key": "type", "value": "file", "type": "text"}, {"key": "file", "type": "file", "src": []}]}, "url": {"raw": "https://api.getpostman.com/import/exported", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["import", "exported"], "variable": [{"key": "import_type", "value": "openapi"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"collections\": [\n        {\n            \"id\": \"e956ff02-7597-4f8d-a5e7-1c18208c9e56\",\n            \"name\": \"Swagger Petstore\",\n            \"uid\": \"2282-e956ff02-7597-4f8d-a5e7-1c18208c9e56\"\n        }\n    ]\n}"}]}], "event": [{"listen": "prerequest", "script": {"id": "5ee3160b-3244-4aae-a1fc-e4d55d65adf3", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "a4a8a112-9506-4940-a5dc-c47c23e73240", "type": "text/javascript", "exec": [""]}}], "protocolProfileBehavior": {}}, {"name": "API", "item": [{"name": "API Version", "item": [{"name": "Create API Version", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"version\": {\n\t\t\"name\": \"1.0\",\n\t\t\"source\": {\n\t\t\t\"id\": \"{{apiVersionId}}\",\n\t\t\t\"schema\": true,\n\t\t\t\"relations\": {\n\t\t\t\t\"monitor\": true,\n\t\t\t\t\"mock\": true,\n\t\t\t\t\"documentation\": true\n\t\t\t}\n\t\t}\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions"]}, "description": "This call creates a new API version in the specified API.  \n\nRequest body should contain a `version` object which should have fields:\n\n<table>\n<tr>\n\t<td>name</td>\n\t<td>Required. Name of the API Version</td>\n</tr>\n<tr>\n\t<td>source</td>\n\t<td>\n\t\tIf specified, it will copy the contents of the specified api version to create a new api verison.\n\t\t<table>\n\t\t\t<tr>\n\t\t\t\t<td>id</td>\n\t\t\t\t<td>Required. Id of the apiversion from where the details are to be copied</td>\n\t\t\t</tr>\n\t\t\t<tr>\n\t\t\t\t<td>schema</td>\n\t\t\t\t<td>Boolean. If true then copies the schema from the specified api version</td>\n\t\t\t</tr>\n\t\t\t<tr>\n\t\t\t\t<td>relations</td>\n\t\t\t\t<td>Object. {\"&lt;relationType&gt;\": true/false}.  \n\t\t\t\t\tAllowed relation types are `mock`, `monitor`, `documentation`, `contracttest`, `integrationtest`, `testsuite`, `environment`\n\t\t\t\tRelation types which should be copied over to the new api verison</td>\n\t\t\t</tr>\n\t\t</table>\n\t</td>\n</tr>\n</table>\n\nResponse contains a `version` object with all the details related to the created API Version, namely, `id`, `name`, `api`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Create API version", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"version\": {\n\t\t\"name\": \"1.0\",\n\t\t\"source\": {\n\t\t\t\"id\": \"{{apiVersionId}}\",\n\t\t\t\"schema\": true,\n\t\t\t\"relations\": {\n\t\t\t\t\"monitor\": true,\n\t\t\t\t\"mock\": true,\n\t\t\t\t\"documentation\": true\n\t\t\t}\n\t\t}\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"version\": {\n\t\t\"id\": \"d71cf403-c549-4c7c-9dc6-a6a105acf67c\",\n\t\t\"name\": \"1.0\",\n    \t\"api\": \"2b95d07c-8379-4bd1-924f-e7e1af185284\"\n\t}\n}"}]}, {"name": "Update an API Version", "request": {"method": "PUT", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"version\": {\n\t\t\"name\": \"2.0\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}"]}, "description": "This call updates an existing API Version.  \n\nRequest body should contain a `version` object with the fields which need to be updated. Only field which can be updated is `name`.\n\nResponse contains a `version` object with all the details related to the created API Version, namely, `id`, `name`, `api` etc.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Update an API version", "originalRequest": {"method": "PUT", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"version\": {\n\t\t\"name\": \"2.0\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{versionId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{versionId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"version\": {\n\t\t\"id\": \"d71cf403-c549-4c7c-9dc6-a6a105acf67c\",\n\t    \"name\": \"2.0\",\n\t    \"createdAt\": \"2019-07-26T11:24:15.000Z\",\n\t    \"updatedAt\": \"2019-08-09T09:27:36.000Z\",\n\t    \"api\": \"2b95d07c-8379-4bd1-924f-e7e1af185284\",\n\t    \"createdBy\": \"12\",\n\t    \"updatedBy\": \"5665\"\n\t}\n}"}]}, {"name": "Delete an API Version", "request": {"method": "DELETE", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}"]}, "description": "This call deletes an existing API Version having the specified id.  \n\nResponse contains an `version` object with `id` of the API Version which was deleted.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Delete an API version", "originalRequest": {"method": "DELETE", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{versionId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{versionId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"version\": {\n\t\t\"id\": \"03c17f53-7e2e-427d-b55a-006b244f29ff\"\n\t}\n}"}]}, {"name": "Get an API Version", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}"]}, "description": "This call fetches details about the specified API Version.\n\nThe response will contain a `version` object which would contain all the details of the API Version.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get an API version", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{versionId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{versionId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"version\": {\n\t\t\"id\": \"03c17f53-7e2e-427d-b55a-006b244f29ff\",\n\t    \"name\": \"0.1\",\n\t    \"api\": \"06e41ca3-8bea-4bc5-a726-70338b9f1940\",\n\t    \"schema\": [\n\t      \"3484cd1e-e00d-4c39-aea4-539663afe898\"\n\t    ],\n\t    \"createdBy\": \"5665\",\n\t    \"updatedBy\": \"5665\",\n\t    \"createdAt\": \"2019-07-21T16:31:15.000Z\",\n\t    \"updatedAt\": \"2019-07-21T16:31:15.000Z\"\n\t}\n}"}]}, {"name": "Get All API Versions", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions"]}, "description": "This call fetches details about the all the API Versions in the specified API.\n\nThe response will contain an array `versions` object which would be a list of all the API Version along with their details.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get All API versions", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"versions\": [\n\t\t{\n\t      \"id\": \"024660a6-c837-46ca-91d8-7e8dd7c669de\",\n\t      \"name\": \"0.1\",\n\t      \"summary\": \"Summary\",\n\t      \"description\": \"Description\",\n\t      \"createdBy\": \"5665\",\n\t      \"createdAt\": \"2019-02-12 19:34:49\",\n\t      \"updatedAt\": \"2019-02-12 19:34:49\"\n\t    },\n\t    {\n\t      \"id\": \"00932d3b-20f1-454f-a77e-38b4023163ea\",\n\t      \"name\": \"0.2\",\n\t      \"summary\": \"Summary\",\n\t      \"description\": \"Description\",\n\t      \"createdBy\": \"5665\",\n\t      \"createdAt\": \"2019-02-12 19:34:49\",\n\t      \"updatedAt\": \"2019-02-12 19:34:49\"\n\t    }\n\t]\n}"}]}], "description": "This folder contains the requests around API versions.", "event": [{"listen": "prerequest", "script": {"id": "2b137fa7-9c1c-4261-bcc2-5b68d29654ac", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "fba3f98e-10f1-4a84-badc-18f44ccf612f", "type": "text/javascript", "exec": [""]}}], "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Create <PERSON><PERSON><PERSON>", "event": [{"listen": "prerequest", "script": {"id": "1443452c-83f3-46a9-a6d5-ff661b98e0dc", "exec": ["const schema = {", "  \"openapi\": \"3.0.0\",", "  \"info\": null,", "  \"version\": \"v1.0\",", "  \"title\": \"APi\",", "  \"servers\": [", "    {", "      \"url\": \"http://localhost:3000\"", "    }", "  ],", "  \"paths\": {", "    \"/user\": {", "      \"get\": {", "        \"summary\": \"Returns details about a particular user\",", "        \"operationId\": \"listUser\",", "        \"tags\": [", "          \"user\"", "        ],", "        \"parameters\": [", "          {", "            \"name\": \"id\",", "            \"in\": \"query\",", "            \"description\": \"ID of the user\",", "            \"required\": true,", "            \"schema\": {", "              \"type\": \"integer\",", "              \"format\": \"int32\"", "            }", "          }", "        ],", "        \"responses\": {", "          \"200\": {", "            \"description\": \"Details about a user by ID\",", "            \"headers\": {", "              \"x-next\": {", "                \"description\": \"A link to the next page of responses\",", "                \"schema\": {", "                  \"type\": \"string\"", "                }", "              }", "            },", "            \"content\": {", "              \"application/json\": {", "                \"schema\": {", "                  \"$ref\": \"\\\\'#/components/schemas/User\\\\'\"", "                }", "              }", "            }", "          },", "          \"default\": {", "            \"description\": \"Unexpected error\",", "            \"content\": {", "              \"application/json\": {", "                \"schema\": {", "                  \"$ref\": \"\\\\'#/components/schemas/Error\\\\'\"", "                }", "              }", "            }", "          }", "        }", "      }", "    }", "  },", "  \"components\": {", "    \"schemas\": {", "      \"User\": {", "        \"type\": \"object\",", "        \"required\": [", "          \"id\",", "          \"name\"", "        ],", "        \"properties\": {", "          \"id\": {", "            \"type\": \"integer\",", "            \"format\": \"int64\"", "          },", "          \"name\": {", "            \"type\": \"string\"", "          },", "          \"tag\": {", "            \"type\": \"string\"", "          }", "        }", "      },", "      \"Error\": {", "        \"type\": \"object\",", "        \"required\": [", "          \"code\",", "          \"message\"", "        ],", "        \"properties\": {", "          \"code\": {", "            \"type\": \"integer\",", "            \"format\": \"int32\"", "          },", "          \"message\": {", "            \"type\": \"string\"", "          }", "        }", "      }", "    }", "  }", "};", "", "pm.environment.set('schema', JSON.stringify(JSON.stringify(schema)));"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"schema\": {\n\t\t\"language\": \"yaml\",\n\t\t\"schema\": {{schema}},\n\t\t\"type\": \"openapi3\"\n\t}\n}", "options": {}}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/schemas", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "schemas"]}, "description": "This call creates a new schema.\n\nRequest body should contain a `schema` object which should atleast have these properties with `string` values:\n\n- **`type`**: Allowed types are `openapi3`, `openapi2`, `openapi1`, `raml` and `graphql`.\n- **`language`** : Allowed languages are `json` and `yaml` for OpenAPI and RAML schema types. GraphQL schema type accepts only `graphql` language.\n- **`schema`**: Optional. If passed, must be the content of the schema as a `string`.\n\nResponse contains a `schema` object with all the details related to the created schema, namely, `id`, `language`, `type` etc. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Create schema", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"schema\": {\n\t\t\"language\": \"yaml\",\n\t\t\"schema\": \"openapi: \\\"3.0.0\\\"↵info:↵  version: 1.0.0↵  title: Swagger Petstore↵  license:↵    name: MIT↵servers:↵  - url: http://petstore.swagger.io/v1↵paths:↵  /pets:↵    get:↵      summary: List all pets↵      operationId: listPets↵      tags:↵        - pets↵      parameters:↵        - name: limit↵          in: query↵          description: How many items to return at one time (max 100)↵          required: false↵          schema:↵            type: integer↵            format: int32↵      responses:↵        200:↵          description: An paged array of pets↵          headers:↵            x-next:↵              description: A link to the next page of responses↵              schema:↵                type: string↵          content:↵            application/json:    ↵              schema:↵                $ref: \\\"#/components/schemas/Pets\\\"↵        default:↵          description: unexpected error↵          content:↵            application/json:↵              schema:↵                $ref: \\\"#/components/schemas/Error\\\"↵    post:↵      summary: Create a pet↵      operationId: createPets↵      tags:↵        - pets↵      responses:↵        201:↵          description: Null response↵        default:↵          description: unexpected error↵          content:↵            application/json:↵              schema:↵                $ref: \\\"#/components/schemas/Error\\\"\",\n\t\t\"type\": \"openapi3\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{versionId}}/schemas", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{versionId}}", "schemas"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"schema\": {\n\t\t\"id\": \"e3b3a0b7-34d5-4fc5-83e0-118bd9e8c822\",\n\t\t\"language\": \"yaml\",\n\t\t\"apiVersion\": \"ad810c39-df60-434e-a76f-a2192cd8d81f\",\n\t\t\"type\": \"openapi3\",\n\t\t\"createdBy\": \"5665\",\n        \"updateBy\": \"5665\",\n\t\t\"createdAt\": \"2019-02-12 19:34:49\",\n\t\t\"updatedAt\": \"2019-02-12 19:34:49\"\n\t}\n}"}]}, {"name": "Update Schema", "event": [{"listen": "prerequest", "script": {"id": "58fbf5d0-a3d1-4e92-b4e7-1f18fbc42484", "exec": ["const schema = {", "  \"openapi\": \"3.0.0\",", "  \"info\": null,", "  \"version\": \"v1.0\",", "  \"title\": \"APi\",", "  \"servers\": [", "    {", "      \"url\": \"http://localhost:3000\"", "    }", "  ],", "  \"paths\": {", "    \"/user\": {", "      \"get\": {", "        \"summary\": \"Returns details about a particular user\",", "        \"operationId\": \"listUser\",", "        \"tags\": [", "          \"user\"", "        ],", "        \"parameters\": [", "          {", "            \"name\": \"id\",", "            \"in\": \"query\",", "            \"description\": \"ID of the user\",", "            \"required\": true,", "            \"schema\": {", "              \"type\": \"integer\",", "              \"format\": \"int32\"", "            }", "          }", "        ],", "        \"responses\": {", "          \"200\": {", "            \"description\": \"Details about a user by ID\",", "            \"headers\": {", "              \"x-next\": {", "                \"description\": \"A link to the next page of responses\",", "                \"schema\": {", "                  \"type\": \"string\"", "                }", "              }", "            },", "            \"content\": {", "              \"application/json\": {", "                \"schema\": {", "                  \"$ref\": \"\\\\'#/components/schemas/User\\\\'\"", "                }", "              }", "            }", "          },", "          \"default\": {", "            \"description\": \"Unexpected error\",", "            \"content\": {", "              \"application/json\": {", "                \"schema\": {", "                  \"$ref\": \"\\\\'#/components/schemas/Error\\\\'\"", "                }", "              }", "            }", "          }", "        }", "      }", "    }", "  },", "  \"components\": {", "    \"schemas\": {", "      \"User\": {", "        \"type\": \"object\",", "        \"required\": [", "          \"id\",", "          \"name\"", "        ],", "        \"properties\": {", "          \"id\": {", "            \"type\": \"integer\",", "            \"format\": \"int64\"", "          },", "          \"name\": {", "            \"type\": \"string\"", "          },", "          \"tag\": {", "            \"type\": \"string\"", "          }", "        }", "      },", "      \"Error\": {", "        \"type\": \"object\",", "        \"required\": [", "          \"code\",", "          \"message\"", "        ],", "        \"properties\": {", "          \"code\": {", "            \"type\": \"integer\",", "            \"format\": \"int32\"", "          },", "          \"message\": {", "            \"type\": \"string\"", "          }", "        }", "      }", "    }", "  }", "};", "", "pm.environment.set('schema', JSON.stringify(JSON.stringify(schema)));"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"schema\": {\n\t\t\"language\": \"json\",\n\t\t\"schema\": {{schema}},\n\t\t\"type\": \"openapi3\"\n\t}\n}", "options": {}}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/schemas/{{schemaId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "schemas", "{{schemaId}}"]}, "description": "This call updates an existing schema.\n\nRequest body should contain a `schema` object which should atleast have these properties with `string` values:\n\n- **`type`**: Allowed types are `openapi3`, `openapi2`, `openapi1`, `raml` and `graphql`.\n- **`language`** : Allowed languages are `json` and `yaml` for OpenAPI and RAML schema types. GraphQL schema type accepts only `graphql` language.\n- **`schema`**: Optional. If passed, must be the content of the schema as a `string`.\n\nResponse contains a `schema` object with all the details related to the created schema, namely, `id`, `language`, `type` etc. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Update schema", "originalRequest": {"method": "PUT", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"schema\": {\n\t\t\"language\": \"yaml\",\n\t\t\"schema\": \"new schema\",\n\t\t\"type\": \"openapi3\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{versionId}}/schemas/{{schemaId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{versionId}}", "schemas", "{{schemaId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"schema\": {\n\t\t\"id\": \"e3b3a0b7-34d5-4fc5-83e0-118bd9e8c822\",\n\t\t\"language\": \"yaml\",\n\t\t\"apiVersion\": \"ad810c39-df60-434e-a76f-a2192cd8d81f\",\n\t\t\"type\": \"openapi3\",\n\t\t\"createdBy\": \"5665\",\n        \"updateBy\": \"5665\",\n\t\t\"createdAt\": \"2019-02-12 19:34:49\",\n\t\t\"updatedAt\": \"2019-02-12 19:34:49\"\n\t}\n}"}]}, {"name": "<PERSON>", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/schemas/{{schemaId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "schemas", "{{schemaId}}"]}, "description": "This call fetches a single schema having the specified id.\n\nResponse contains a `schema` object with all the details related to the schema, namely, `id`, `language`, `type`, `schema` etc. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get schema", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/schemas/{{schemaId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "schemas", "{{schemaId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"schema\": {\n\t\t\"id\": \"e3b3a0b7-34d5-4fc5-83e0-118bd9e8c822\",\n\t\t\"language\": \"yaml\",\n\t\t\"apiVersion\": \"ad810c39-df60-434e-a76f-a2192cd8d81f\",\n\t\t\"type\": \"openapi3\",\n\t\t\"createdBy\": \"5665\",\n        \"updateBy\": \"5665\",\n\t\t\"createdAt\": \"2019-02-12 19:34:49\",\n\t\t\"updatedAt\": \"2019-02-12 19:34:49\"\n\t}\n}"}]}, {"name": "Create collection from schema", "request": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"My generated collection\",\n\t\"relations\": [\n\t\t{\n\t\t\t\"type\": \"contracttest\"\n\t\t}\t\n\t]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/schemas/{{schemaId}}/collections?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "schemas", "{{schemaId}}", "collections"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}]}, "description": "This call creates a collection and links it to an API as one or multiple relations. \n\nRequest body requires two keys:\n- `name` - Name of the collection to be created.\n- `relations` - A list of relation(s) to be created.\n\nThe allowed relation types are `contracttest`, `integrationtest`, `testsuite`, and `documentation`.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Create collection from schema", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"name\": \"My generated collection\",\n\t\"relations\": [\n\t\t{\n\t\t\t\"type\": \"contracttest\"\n\t\t}\t\n\t]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/schemas/{{schemaId}}/collections?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "schemas", "{{schemaId}}", "collections"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Date", "value": "Mon, 27 Apr 2020 14:13:42 GMT"}, {"key": "ETag", "value": "W/\"f7-MwDfy+rfXKvxn9z+q9h9J1oracg\""}, {"key": "Server", "value": "nginx"}, {"key": "x-frame-options", "value": "SAMEORIGIN"}, {"key": "x-srv-span", "value": "v=1;s=55843a5ae1e3e829"}, {"key": "x-srv-trace", "value": "v=1;t=14cc67b6eb86260d"}, {"key": "Content-Length", "value": "173"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"collection\": {\n        \"id\": \"e6b0d46a-8722-4f42-ab86-f5f473187ddf\",\n        \"uid\": \"112098-e6b0d46a-8722-4f42-ab86-f5f473187ddf\"\n    },\n    \"relations\": [\n        {\n            \"type\": \"contracttest\",\n            \"id\": \"4b40f06a-5a6a-448f-bfcd-a6dbcb68da22\"\n        }\n    ]\n}"}]}], "description": "This folder contains all the endpoints related to <PERSON><PERSON><PERSON>.", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Relations", "item": [{"name": "Get linked relations", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/relations", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "relations"]}, "description": "This call fetches all the relations which are linked to the specified API version along with their details.\n\nThe response will contain a `relations` object which lists all the relation types which are linked to the API version. Each of the relation type will be an object which details the relations of this type. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get linked relations for an API version", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/relations", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "relations"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"relations\": {\n\t\t\"contracttest\": {\n            \"2a9b8fa8-88b7-4b86-8372-8e3f6f6e07f2\": {\n                \"id\": \"2a9b8fa8-88b7-4b86-8372-8e3f6f6e07f2\",\n                \"name\": \"C test\",\n                \"updatedAt\": \"2019-08-29T10:18:11.000Z\"\n            }\n        },\n        \"integrationtest\": {\n            \"521b0486-ab91-4d3a-9189-43c9380a0533\": {\n                \"id\": \"521b0486-ab91-4d3a-9189-43c9380a0533\",\n                \"name\": \"C1\",\n                \"updatedAt\": \"2019-08-29T11:40:39.000Z\"\n            },\n            \"a236b715-e682-460b-97b6-c1db24f7612e\": {\n                \"id\": \"a236b715-e682-460b-97b6-c1db24f7612e\",\n                \"name\": \"C test\",\n                \"updatedAt\": \"2019-08-29T10:18:11.000Z\"\n            }\n        },\n        \"mock\": {\n            \"4ccd755f-2c80-481b-a262-49b55e12f5e1\": {\n                \"id\": \"4ccd755f-2c80-481b-a262-49b55e12f5e1\",\n                \"name\": \"Mock\",\n                \"url\": \"https://4ccd755f-2c80-481b-a262-49b55e12f5e1.mock-beta.pstmn.io\",\n                \"updatedAt\": \"2019-08-20T10:18:13.000Z\"\n            }\n        }\n\t}\n}"}]}, {"name": "Get relations by type", "event": [{"listen": "test", "script": {"id": "6dba9e91-87a5-42e8-9151-ab9096a79260", "exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.environment.set('contracttestid', pm.response.json().contracttest[0].id);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/{{relationType}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "{{relationType}}"]}, "description": "This call fetches all the relations by type which are linked to the specified API version along with their details.\n\nThe response will contain an array with key `{{relationType}}`. Each of the item will consist of all the details related each of the relation.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get relations of a type", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/relations/contracttest", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "relations", "contracttest"]}}, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"contracttest\": [\n        {\n            \"id\": \"2a9b8fa8-88b7-4b86-8372-8e3f6f6e07f2\",\n            \"name\": \"C test\",\n            \"updatedAt\": \"2019-08-29T10:18:11.000Z\",\n            \"collectionId\": \"7732157-a8bcd149-2b01-4b4c-8c14-c7d05be77745\"\n        },\n        {\n            \"id\": \"521b0486-ab91-4d3a-9189-43c9380a0533\",\n            \"name\": \"C1\",\n            \"updatedAt\": \"2019-08-29T11:40:39.000Z\",\n            \"collectionId\": \"7332157-a8bcd143-2b01-4b12-8c14-c7d05be77725\"\n        }\n    ]\n}"}]}, {"name": "Get relation details", "request": {"method": "GET", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/{{relationType}}/{{relationId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "{{relationType}}", "{{relationId}}"]}, "description": "This call fetches detailed information of a relation.\n\nThe response will contain keys as shown in the example response where `collection` will contain the collection associated with the relation in <a href=\"https://schema.getpostman.com/collection/json/v2.1.0/draft-07/docs/index.html\">v2.1.0 format</a>. \n\nFor mocks, monitors, and environments, the response will contain the ID of the entity.\n \n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get relations of a type", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/relations/{{relationType}}/{{contracttestId}}?populate=collection", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "relations", "{{relationType}}", "{{contracttestId}}"], "query": [{"key": "populate", "value": "collection"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": \"2a9b8fa8-88b7-4b86-8372-8e3f6f6e07f2\",\n    \"name\": \"C test\",\n    \"updatedAt\": \"2019-08-29T10:18:11.000Z\",\n    \"createdAt\": \"2019-08-29T10:18:11.000Z\",\n    \"versionTag\": {\n    \t\"name\": \"v1.0.1\",\n    \t\"id\": \"53eed8bf-1f73-4cf2-a8f0-02ae4a959f03\"\n    },\n    \"collection\": {\n        \"info\": {\n            \"_postman_id\": \"5cfe4ee2-10d0-49e5-ac3e-a1a647c37f2d\",\n            \"name\": \"Generated Collection - 2\",\n            \"schema\": \"https://schema.getpostman.com/json/collection/v2.1.0/collection.json\"\n        },\n        \"item\": [\n            {\n                \"name\": \"pets\",\n                \"item\": [\n                    {\n                        \"name\": \"List all pets\",\n                        \"_postman_id\": \"b80150b6-fb5e-4e48-88ab-de4bfba06112\",\n                        \"request\": {\n                            \"auth\": {\n                                \"type\": \"noauth\"\n                            },\n                            \"method\": \"GET\",\n                            \"header\": [],\n                            \"url\": {\n                                \"raw\": \"{{baseUrl}}/pets?limit=\\n<integer>\",\n                                \"host\": [\n                                    \"{{baseUrl}}\"\n                                ],\n                                \"path\": [\n                                    \"pets\"\n                                ],\n                                \"query\": [\n                                    {\n                                        \"description\": \"How many items to return at one time (max 100)\",\n                                        \"key\": \"limit\",\n                                        \"value\": \"\\n    <integer>\"\n                                    }\n                                ]\n                            }\n                        },\n                        \"response\": [\n                            {\n                                \"id\": \"b1f75aee-327e-412f-bf5a-74c9b01ef605\",\n                                \"name\": \"A paged array of pets\",\n                                \"originalRequest\": {\n                                    \"method\": \"GET\",\n                                    \"header\": [],\n                                    \"url\": {\n                                        \"raw\": \"{{baseUrl}}/pets?limit=\\n        <integer>\",\n                                        \"host\": [\n                                            \"{{baseUrl}}\"\n                                        ],\n                                        \"path\": [\n                                            \"pets\"\n                                        ],\n                                        \"query\": [\n                                            {\n                                                \"key\": \"limit\",\n                                                \"value\": \"\\n            <integer>\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                \"status\": \"OK\",\n                                \"code\": 200,\n                                \"_postman_previewlanguage\": \"json\",\n                                \"header\": [\n                                    {\n                                        \"key\": \"x-next\",\n                                        \"value\": \"\\n                <string>\",\n                                        \"description\": \"A link to the next page of responses\"\n                                    },\n                                    {\n                                        \"key\": \"Content-Type\",\n                                        \"value\": \"application/json\"\n                                    }\n                                ],\n                                \"cookie\": [],\n                                \"responseTime\": null,\n                                \"body\": \"[\\n {\\n  \\\"id\\\": \\\"\\n                    <long>\\\",\\n  \\\"name\\\": \\\"\\n                        <string>\\\",\\n  \\\"tag\\\": \\\"\\n                            <string>\\\"\\n },\\n {\\n  \\\"id\\\": \\\"\\n                                <long>\\\",\\n  \\\"name\\\": \\\"\\n                                    <string>\\\",\\n  \\\"tag\\\": \\\"\\n                                        <string>\\\"\\n }\\n]\"\n                            },\n                            {\n                                \"id\": \"f6260b7e-5c90-4b5f-98d5-41f04e04108a\",\n                                \"name\": \"unexpected error\",\n                                \"originalRequest\": {\n                                    \"method\": \"GET\",\n                                    \"header\": [],\n                                    \"url\": {\n                                        \"raw\": \"{{baseUrl}}/pets?limit=\\n                                            <integer>\",\n                                        \"host\": [\n                                            \"{{baseUrl}}\"\n                                        ],\n                                        \"path\": [\n                                            \"pets\"\n                                        ],\n                                        \"query\": [\n                                            {\n                                                \"key\": \"limit\",\n                                                \"value\": \"\\n                                                <integer>\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                \"status\": \"Internal Server Error\",\n                                \"code\": 500,\n                                \"_postman_previewlanguage\": \"json\",\n                                \"header\": [\n                                    {\n                                        \"key\": \"Content-Type\",\n                                        \"value\": \"application/json\"\n                                    }\n                                ],\n                                \"cookie\": [],\n                                \"responseTime\": null,\n                                \"body\": \"{\\n \\\"code\\\": \\\"\\n                                                    <integer>\\\",\\n \\\"message\\\": \\\"\\n                                                        <string>\\\"\\n}\"\n                            }\n                        ]\n                    },\n                    {\n                        \"name\": \"Create a pet\",\n                        \"_postman_id\": \"a5e21c45-2389-4571-a3b3-2d0f7d6aac6a\",\n                        \"request\": {\n                            \"auth\": {\n                                \"type\": \"noauth\"\n                            },\n                            \"method\": \"POST\",\n                            \"header\": [],\n                            \"url\": {\n                                \"raw\": \"{{baseUrl}}/pets\",\n                                \"host\": [\n                                    \"{{baseUrl}}\"\n                                ],\n                                \"path\": [\n                                    \"pets\"\n                                ]\n                            }\n                        },\n                        \"response\": [\n                            {\n                                \"id\": \"a052da96-a327-4b83-8e7e-66e97249f6d3\",\n                                \"name\": \"unexpected error\",\n                                \"originalRequest\": {\n                                    \"method\": \"POST\",\n                                    \"header\": [],\n                                    \"url\": {\n                                        \"raw\": \"{{baseUrl}}/pets\",\n                                        \"host\": [\n                                            \"{{baseUrl}}\"\n                                        ],\n                                        \"path\": [\n                                            \"pets\"\n                                        ]\n                                    }\n                                },\n                                \"status\": \"Internal Server Error\",\n                                \"code\": 500,\n                                \"_postman_previewlanguage\": \"json\",\n                                \"header\": [\n                                    {\n                                        \"key\": \"Content-Type\",\n                                        \"value\": \"application/json\"\n                                    }\n                                ],\n                                \"cookie\": [],\n                                \"responseTime\": null,\n                                \"body\": \"{\\n \\\"code\\\": \\\"\\n                                                            <integer>\\\",\\n \\\"message\\\": \\\"\\n                                                                <string>\\\"\\n}\"\n                            },\n                            {\n                                \"id\": \"e1e42585-2624-4f02-bc5c-b433ef2cad2c\",\n                                \"name\": \"Null response\",\n                                \"originalRequest\": {\n                                    \"method\": \"POST\",\n                                    \"header\": [],\n                                    \"url\": {\n                                        \"raw\": \"{{baseUrl}}/pets\",\n                                        \"host\": [\n                                            \"{{baseUrl}}\"\n                                        ],\n                                        \"path\": [\n                                            \"pets\"\n                                        ]\n                                    }\n                                },\n                                \"status\": \"Created\",\n                                \"code\": 201,\n                                \"_postman_previewlanguage\": \"text\",\n                                \"header\": [\n                                    {\n                                        \"key\": \"Content-Type\",\n                                        \"value\": \"text/plain\"\n                                    }\n                                ],\n                                \"cookie\": [],\n                                \"responseTime\": null,\n                                \"body\": \"\"\n                            }\n                        ]\n                    },\n                    {\n                        \"name\": \"Info for a specific pet\",\n                        \"_postman_id\": \"9307687f-115f-40ac-ad1d-15df249f7876\",\n                        \"request\": {\n                            \"auth\": {\n                                \"type\": \"noauth\"\n                            },\n                            \"method\": \"GET\",\n                            \"header\": [],\n                            \"url\": {\n                                \"raw\": \"{{baseUrl}}/pets/:petId\",\n                                \"host\": [\n                                    \"{{baseUrl}}\"\n                                ],\n                                \"path\": [\n                                    \"pets\",\n                                    \":petId\"\n                                ],\n                                \"variable\": [\n                                    {\n                                        \"id\": \"2b0bb9bb-dd8c-4fd9-ac5f-4bf6a7188bdd\",\n                                        \"key\": \"petId\",\n                                        \"value\": \"\\n                                                                    <string>\",\n                                        \"type\": \"string\",\n                                        \"description\": \"(Required) The id of the pet to retrieve\"\n                                    }\n                                ]\n                            }\n                        },\n                        \"response\": [\n                            {\n                                \"id\": \"b3893689-4838-40e7-8f4c-0ad2539e4018\",\n                                \"name\": \"Expected response to a valid request\",\n                                \"originalRequest\": {\n                                    \"method\": \"GET\",\n                                    \"header\": [],\n                                    \"url\": {\n                                        \"raw\": \"{{baseUrl}}/pets/:petId\",\n                                        \"host\": [\n                                            \"{{baseUrl}}\"\n                                        ],\n                                        \"path\": [\n                                            \"pets\",\n                                            \":petId\"\n                                        ],\n                                        \"variable\": [\n                                            {\n                                                \"key\": \"petId\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                \"status\": \"OK\",\n                                \"code\": 200,\n                                \"_postman_previewlanguage\": \"json\",\n                                \"header\": [\n                                    {\n                                        \"key\": \"Content-Type\",\n                                        \"value\": \"application/json\"\n                                    }\n                                ],\n                                \"cookie\": [],\n                                \"responseTime\": null,\n                                \"body\": \"{\\n \\\"id\\\": \\\"\\n                                                                        <long>\\\",\\n \\\"name\\\": \\\"\\n                                                                            <string>\\\",\\n \\\"tag\\\": \\\"\\n                                                                                <string>\\\"\\n}\"\n                            },\n                            {\n                                \"id\": \"b56180aa-dbd0-40ad-8b1a-d2caaf8a1df7\",\n                                \"name\": \"unexpected error\",\n                                \"originalRequest\": {\n                                    \"method\": \"GET\",\n                                    \"header\": [],\n                                    \"url\": {\n                                        \"raw\": \"{{baseUrl}}/pets/:petId\",\n                                        \"host\": [\n                                            \"{{baseUrl}}\"\n                                        ],\n                                        \"path\": [\n                                            \"pets\",\n                                            \":petId\"\n                                        ],\n                                        \"variable\": [\n                                            {\n                                                \"key\": \"petId\"\n                                            }\n                                        ]\n                                    }\n                                },\n                                \"status\": \"Internal Server Error\",\n                                \"code\": 500,\n                                \"_postman_previewlanguage\": \"json\",\n                                \"header\": [\n                                    {\n                                        \"key\": \"Content-Type\",\n                                        \"value\": \"application/json\"\n                                    }\n                                ],\n                                \"cookie\": [],\n                                \"responseTime\": null,\n                                \"body\": \"{\\n \\\"code\\\": \\\"\\n                                                                                    <integer>\\\",\\n \\\"message\\\": \\\"\\n                                                                                        <string>\\\"\\n}\"\n                            }\n                        ]\n                    }\n                ],\n                \"_postman_id\": \"1d185956-a438-4248-9fa5-33598a892d23\"\n            }\n        ],\n        \"variable\": [\n            {\n                \"id\": \"baseUrl\",\n                \"key\": \"baseUrl\",\n                \"value\": \"http://petstore.swagger.io/v1\",\n                \"type\": \"string\"\n            }\n        ]\n    }\n}"}]}, {"name": "Create relations", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"contracttest\": [\"collection-uid-1\"],\n\t\"testsuite\": [\"collection-uid-1\", \"collection-uid-2\"],\n\t\"documentation\": [\"collection-uid-3\"],\n\t\"mock\": [\"mock-uid-1\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/relations", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "relations"]}, "description": "This call allows you to add existing Postman entities as new relations to an API. The request body should contain the relations to be created along with an array of entity IDs. \n\n<table>\n  <tr>\n    <th>Relation</th>\n    <th>Entity ID type</th>\n  <tr>\n  <tr>\n    <td>contracttest</td>\n    <td>Collection UIDs</td>\n  </tr>\n  <tr>\n    <td>integrationtest</td>\n    <td>Collection UIDs</td>\n  </tr>\n  <tr>\n    <td>documentation</td>\n    <td>Collection UIDs</td>\n  </tr>\n  <tr>\n    <td>testsuite</td>\n    <td>Collection UIDs</td>\n  </tr>\n  <tr>\n    <td>environment</td>\n    <td>Environment UIDs</td>\n  </tr>\n  <tr>\n    <td>mock</td>\n    <td>Mock IDs</td>\n  </tr>\n  <tr>\n    <td>monitor</td>\n    <td>Monitor IDs</td>\n  </tr>\n</table>\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Create multiple relations from existing collections", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"contracttest\": [\"112098-a173931c-3340-4c14-b30f-ae62e5fbeb52\"],\n\t\"testsuite\": [\"112098-a173931c-3340-4c14-b30f-ae62e5fbeb52\", \"112098-47958a5a-a2a5-4330-9c9d-60c5166115f2\"],\n\t\"documentation\": [\"112098-47958a5a-a2a5-4330-9c9d-60c5166115f2\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}/versions/{{apiVersionId}}/relations", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}", "versions", "{{apiVersionId}}", "relations"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Encoding", "value": "gzip"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Date", "value": "Thu, 23 Apr 2020 09:39:36 GMT"}, {"key": "ETag", "value": "W/\"f8-8No3oL8RvapeIu5fRUddh4X7Mx0\""}, {"key": "Server", "value": "nginx"}, {"key": "x-frame-options", "value": "SAMEORIGIN"}, {"key": "X-RateLimit-Limit", "value": "60"}, {"key": "X-RateLimit-Remaining", "value": "59"}, {"key": "X-RateLimit-Reset", "value": "1587634833"}, {"key": "x-srv-span", "value": "v=1;s=e17157dc347750d4"}, {"key": "x-srv-trace", "value": "v=1;t=d0f4d4cdfafd0ad3"}, {"key": "Content-Length", "value": "187"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"contracttest\": [\n        \"5bcece87-ca4b-4e75-a967-2a6845626164\"\n    ],\n    \"testsuite\": [\n        \"e525fa71-035e-4620-acda-ce878524f1e7\",\n        \"17a974b2-ce79-4b95-9d3f-217d6ff7e979\"\n    ],\n    \"documentation\": [\n        \"2084eba6-a17b-4751-8f03-ea60f30ba19c\"\n    ]\n}"}]}], "description": "This contains requests related to relations. \n\nRelations can be of the following types: \n- `contracttest`\n- `integrationtest`\n- `documentation`\n- `testsuite`\n- `environment`\n- `mock`\n- `monitor`", "protocolProfileBehavior": {}, "_postman_isSubFolder": true}, {"name": "Create API", "event": [{"listen": "test", "script": {"id": "588cafee-17b5-4b36-ba5d-7b6fb6cfcfd4", "exec": ["const jsonData = pm.response.json();", "", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Should have valid response\", function () {", "    pm.expect(jsonData.data).to.include.all.keys('id', 'name', 'createdAt', 'createdBy', 'updatedAt', 'updatedBy');", "    ", "    pm.expect(jsonData.data.name).to.be.equal('New API');", "    pm.expect(jsonData.data.id).to.be.ok;", "    pm.expect(jsonData.data.createdAt).to.be.ok;", "    pm.expect(jsonData.data.createdBy).to.be.ok;", "    pm.expect(jsonData.data.updatedAt).to.be.ok;", "    pm.expect(jsonData.data.updatedBy).to.be.ok;", "});", "", "pm.environment.set(\"apiId\", jsonData.data.id); "], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}, {"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n\t\"api\": {\n\t\t\"name\": \"Sync Service API\",\n\t\t\"summary\": \"This is supposed to be a short summary.\",\n\t\t\"description\": \"This is description.\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}]}, "description": "This call creates a new API with a default API Version.  \n\nRequest body should contain an `api` object which should atleast have a property `name`.\n\nResponse contains an `api` object with all the details related to the created API, namely, `id`, `name`, `summary`, `description`, etc. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Create API", "originalRequest": {"method": "POST", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"api\": {\n\t\t\"name\": \"Sync Service API\",\n\t\t\"summary\": \"This is supposed to be a short summary.\",\n\t\t\"description\": \"This is description.\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}]}}, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"api\": {\n\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edade\",\n\t\t\"name\": \"Sync API\",\n\t\t\"summary\": \"This is a summary\",\n\t\t\"description\": \"This is supposed to handle markdown *descriptions*.\",\n\t\t\"createdBy\": \"5665\",\n\t\t\"createdAt\": \"2019-02-12 19:34:49\",\n\t\t\"updatedAt\": \"2019-02-12 19:34:49\"\n\t}\n}"}]}, {"name": "Update an API", "request": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "type": "text", "value": "application/json"}, {"key": "x-api-key", "type": "text", "value": "{{postman_api_key}}"}], "body": {"mode": "raw", "raw": "{\n\t\"api\": {\n\t\t\"name\": \"New name\",\n\t\t\"description\": \"This is an updated Description\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}"]}, "description": "This call updates an existing API .  \n\nRequest body should contain an `api` object which should have the fields to be updated. Only `name`, `summary` and `description` fields can be edited for an API.\n\nResponse contains an `api` object with all the details related to the created API, namely, `id`, `name`, `summary`, `description` etc. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Update an API", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}, {"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n\t\"api\": {\n\t\t\"name\": \"New name\",\n\t\t\"description\": \"This is an updated Description\"\n\t}\n}"}, "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"api\":{\n\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edade\",\n\t\t\"name\": \"Sync API\",\n\t\t\"summary\": \"This is a summary\",\n\t\t\"description\": \"This is a description.\",\n\t\t\"createdBy\": \"5665\",\n\t\t\"createdAt\": \"2019-02-12 19:34:49\",\n\t\t\"updatedAt\": \"2019-02-12 19:34:49\"\n\t}\n}"}]}, {"name": "Delete an API", "request": {"method": "DELETE", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}"]}, "description": "This call deletes an existing API having the specified id.  \n\nResponse contains an `api` object with `id` of the API which was deleted.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Delete an API", "originalRequest": {"method": "DELETE", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"api\": {\n\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edade\"\n\t}\n}"}]}, {"name": "Get all APIs", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}]}, "description": "This call fetches all the APIs present in the specified workspace\n\nResponse contains an array named `apis` which would contain all the details of APIs present in the workspace.\n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get all APIs", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis?workspace={{workspaceId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis"], "query": [{"key": "workspace", "value": "{{workspaceId}}"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"apis\": [\n\t\t{\n\t\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edade\",\n\t\t     \"name\": \"Sync API\",\n\t\t     \"summary\": \"This is a summary\",\n\t\t     \"description\": \"This is a description.\",\n\t\t     \"createdBy\": \"5665\",\n\t\t     \"createdAt\": \"2019-02-12 19:34:49\",\n\t\t     \"updatedAt\": \"2019-02-12 19:34:49\"\n\t\t},\n\t\t{\n\t\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edade\",\n\t\t     \"name\": \"Sync API\",\n\t\t     \"summary\": \"This is a summary\",\n\t\t     \"description\": \"This is a description.\",\n\t\t     \"createdBy\": \"5665\",\n\t\t     \"createdAt\": \"2019-02-12 19:34:49\",\n\t\t     \"updatedAt\": \"2019-02-12 19:34:49\"\n\t\t},\n\t\t{\n\t\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edhdh\",\n\t\t     \"name\": \"Sync API 2\",\n\t\t     \"summary\": \"This is a summary\",\n\t\t     \"description\": \"This is a description.\",\n\t\t     \"createdBy\": \"5665\",\n\t\t     \"createdAt\": \"2019-02-12 19:34:49\",\n\t\t     \"updatedAt\": \"2019-02-12 19:34:49\"\n\t\t}\n\t]\n}"}]}, {"name": "Single API", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}"]}, "description": "This call fetches a single API having the specified id.\n\nResponse contains an `api` object with all the details related to the queried API, namely, `id`, `name`, `summary`, `description` etc. \n\n> Requires <a href=\"#authentication\">API Key</a> as `X-Api-Key` request header or `apikey` URL query parameter."}, "response": [{"name": "Get single API", "originalRequest": {"method": "GET", "header": [{"key": "x-api-key", "value": "{{postman_api_key}}", "type": "text"}, {"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://api.getpostman.com/apis/{{apiId}}", "protocol": "https", "host": ["api", "get<PERSON>man", "com"], "path": ["apis", "{{apiId}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n\t\"api\": {\n\t\t\"id\": \"387c2863-6ee3-4a56-8210-225f774edade\",\n\t\t\"name\": \"Sync API\",\n\t\t\"summary\": \"This is a summary\",\n\t\t\"description\": \"This is a description.This is a description.\",\n\t\t\"createdBy\": \"5665\",\n\t\t\"createdAt\": \"2019-02-12 19:34:49\",\n\t\t\"updatedAt\": \"2019-02-12 19:34:49\"\n\t}\n}"}]}], "protocolProfileBehavior": {}}], "event": [{"listen": "prerequest", "script": {"id": "40504fdc-c897-4cb3-a7fe-308aa0914869", "type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"id": "3c6d2d74-1bf1-485d-8e25-11e8864f003a", "type": "text/javascript", "exec": [""]}}], "variable": [{"id": "dfd465cc-2412-4b30-8518-969a91650020", "key": "postman_api_key", "value": ""}], "protocolProfileBehavior": {}}