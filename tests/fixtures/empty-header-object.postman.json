{"info": {"_postman_id": "2a763711-fdbf-4c82-a7eb-f27c3db887e7", "name": "Empty Header Object example", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "updatedAt": "2023-11-22T17:23:18.000Z", "uid": "10354132-2a763711-fdbf-4c82-a7eb-f27c3db887e7"}, "item": [{"name": "GET Request", "event": [{"listen": "test", "script": {"id": "2298dced-107f-4b06-afe7-8f1835d4477f", "exec": ["pm.test(\"response is ok\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"response body has json with request queries\", function () {", "    pm.response.to.have.jsonBody('args.foo1', 'bar1')", "        .and.have.jsonBody('args.foo2', 'bar2');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "https://postman-echo.com/get?foo1=bar1&foo2=bar2", "protocol": "https", "host": ["postman-echo", "com"], "path": ["get"], "query": [{"key": "foo1", "value": "bar1"}, {"key": "foo2", "value": "bar2"}]}, "description": "The HTTP `GET` request method is meant to retrieve data from a server. The data\nis identified by a unique URI (Uniform Resource Identifier). \n\nA `GET` request can pass parameters to the server using \"Query String \nParameters\". For example, in the following request,\n\n> http://example.com/hi/there?hand=wave\n\nThe parameter \"hand\" has the value \"wave\".\n\nThis endpoint echoes the HTTP headers, request parameters and the complete\nURI requested."}, "response": [{"name": "GET Request Woops", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "https://postman-echo.com/get?foo1=bar1&foo2=bar2", "protocol": "https", "host": ["postman-echo", "com"], "path": ["get"], "query": [{"key": "foo1", "value": "bar1"}, {"key": "foo2", "value": "bar2"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Date", "value": "<PERSON><PERSON>, 11 Jun 2019 10:43:13 GMT"}, {"key": "ETag", "value": "W/\"161-aLhNcsGArlgLSKbxPqfBW3viHPI\""}, {"key": "Server", "value": "nginx"}, {"key": "set-cookie", "value": "sails.sid=s%3AGz-wblZgXE8FCDq7aJpx_tUgZUcG3Nsw.LdNEN8L0C7nGWkvGLwvdw6R2s6Syjr%2FzkvyevA8qR0c; Path=/; HttpOnly"}, {"key": "Vary", "value": "Accept-Encoding"}, {"key": "Content-Length", "value": "249"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"args\": {\n        \"foo1\": \"bar1\",\n        \"foo2\": \"bar2\"\n    },\n    \"headers\": {\n        \"x-forwarded-proto\": \"https\",\n        \"host\": \"postman-echo.com\",\n        \"accept\": \"*/*\",\n        \"accept-encoding\": \"gzip, deflate\",\n        \"cache-control\": \"no-cache\",\n        \"postman-token\": \"5c27cd7d-6b16-4e5a-a0ef-191c9a3a275f\",\n        \"user-agent\": \"PostmanRuntime/7.6.1\",\n        \"x-forwarded-port\": \"443\"\n    },\n    \"url\": \"https://postman-echo.com/get?foo1=bar1&foo2=bar2\"\n}"}]}]}