{"openapi": "3.0.3", "info": {"title": "Postman Echo", "description": "Postman Echo is service you can use to test your REST clients and make sample API calls. It provides endpoints for `GET`, `POST`, `PUT`, various auth mechanisms and other utility endpoints.\n\nThe documentation for the endpoints as well as example responses can be found at [https://postman-echo.com](https://postman-echo.com/?source=echo-collection-app-onboarding)", "version": "1.0.0", "contact": {}}, "servers": [{"url": "https://postman-echo.com"}], "paths": {"/get": {"get": {"tags": ["Request Methods"], "summary": "GET Request", "description": "The HTTP `GET` request method is meant to retrieve data from a server. The data\nis identified by a unique URI (Uniform Resource Identifier). \n\nA `GET` request can pass parameters to the server using \"Query String \nParameters\". For example, in the following request,\n\n> http://example.com/hi/there?hand=wave\n\nThe parameter \"hand\" has the value \"wave\".\n\nThis endpoint echoes the HTTP headers, request parameters and the complete\nURI requested.", "operationId": "getRequest", "parameters": [{"name": "foo1", "in": "query", "schema": {"type": "string", "example": "bar1"}}, {"name": "foo2", "in": "query", "schema": {"type": "string", "example": "bar2"}}], "responses": {"200": {"description": "GET Request Woops", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "249"}}, "Date": {"schema": {"type": "string", "example": "<PERSON><PERSON>, 11 Jun 2019 10:43:13 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"161-aLhNcsGArlgLSKbxPqfBW3viHPI\""}}, "Server": {"schema": {"type": "string", "example": "nginx"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "set-cookie": {"schema": {"type": "string", "example": "sails.sid=s%3AGz-wblZgXE8FCDq7aJpx_tUgZUcG3Nsw.LdNEN8L0C7nGWkvGLwvdw6R2s6Syjr%2FzkvyevA8qR0c; Path=/; HttpOnly"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"args": {"type": "object", "properties": {"foo1": {"type": "string", "example": "bar1"}, "foo2": {"type": "string", "example": "bar2"}}}, "headers": {"type": "object", "properties": {"accept": {"type": "string", "example": "*/*"}, "accept-encoding": {"type": "string", "example": "gzip, deflate"}, "cache-control": {"type": "string", "example": "no-cache"}, "host": {"type": "string", "example": "postman-echo.com"}, "postman-token": {"type": "string", "example": "5c27cd7d-6b16-4e5a-a0ef-191c9a3a275f"}, "user-agent": {"type": "string", "example": "PostmanRuntime/7.6.1"}, "x-forwarded-port": {"type": "string", "example": "443"}, "x-forwarded-proto": {"type": "string", "example": "https"}}}, "url": {"type": "string", "example": "https://postman-echo.com/get?foo1=bar1&foo2=bar2"}}}, "examples": {"GET Request Woops": {"value": {"args": {"foo1": "bar1", "foo2": "bar2"}, "headers": {"accept": "*/*", "accept-encoding": "gzip, deflate", "cache-control": "no-cache", "host": "postman-echo.com", "postman-token": "5c27cd7d-6b16-4e5a-a0ef-191c9a3a275f", "user-agent": "PostmanRuntime/7.6.1", "x-forwarded-port": "443", "x-forwarded-proto": "https"}, "url": "https://postman-echo.com/get?foo1=bar1&foo2=bar2"}}}}}}}}}, "/post": {"post": {"tags": ["Request Methods"], "summary": "POST Raw Text", "description": "The HTTP `POST` request method is meant to transfer data to a server \n(and elicit a response). What data is returned depends on the implementation\nof the server.\n\nA `POST` request can pass parameters to the server using \"Query String \nParameters\", as well as the Request Body. For example, in the following request,\n\n> POST /hi/there?hand=wave\n>\n> <request-body>\n\nThe parameter \"hand\" has the value \"wave\". The request body can be in multiple\nformats. These formats are defined by the MIME type of the request. The MIME \nType can be set using the ``Content-Type`` HTTP header. The most commonly used \nMIME types are:\n\n* `multipart/form-data`\n* `application/x-www-form-urlencoded`\n* `application/json`\n\nThis endpoint echoes the HTTP headers, request parameters, the contents of\nthe request body and the complete URI requested.", "operationId": "postRawText", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"type": "object", "properties": {"foo1": {"type": "string", "example": "bar1"}, "foo2": {"type": "string", "example": "bar2"}}}, "examples": {"POST Form Data": {"value": {"foo1": "bar1", "foo2": "bar2"}}}}, "text/plain": {"examples": {"POST Raw Text": {"value": "This is expected to be sent back as part of response body."}}}}}, "responses": {"200": {"description": ""}}}}, "/put": {"put": {"tags": ["Custom"], "summary": "PUT Request", "description": "The HTTP `PUT` request method is similar to HTTP `POST`. It too is meant to \ntransfer data to a server (and elicit a response). What data is returned depends on the implementation\nof the server.\n\nA `PUT` request can pass parameters to the server using \"Query String \nParameters\", as well as the Request Body. For example, in the following \nraw HTTP request,\n\n> PUT /hi/there?hand=wave\n>\n> <request-body>\n\n\n", "operationId": "putRequest", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"authenticated": {"type": "boolean", "example": true}, "hello": {"type": "string", "example": "there"}, "is": {"type": "object", "properties": {"legally": {"type": "string", "example": "variable_value"}, "mixed": {"type": "array", "items": {"anyOf": [{"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}}}, {"type": "boolean", "example": true}, {"type": "number", "example": 38}, {"type": "string", "example": "<PERSON>"}]}, "example": [{"name": "<PERSON>"}, true, 38, "<PERSON>"]}, "num": {"type": "number", "example": 1000}}}, "my": {"type": "number", "example": 1}, "name": {"type": "boolean", "example": true}}}, "example": [{"hello": "there"}, {"is": {"legally": "variable_value", "mixed": [{"name": "<PERSON>"}, true, 38, "<PERSON>"], "num": 1000}, "my": 1, "name": true}, {"authenticated": true}]}, "examples": {"PUT Custom": {"value": {"hello": "there", "is": {"legally": "variable_value", "mixed": [{"name": "<PERSON>"}, true, 38, "<PERSON>"], "num": 1000}, "my": 1, "name": true}}, "PUT Custom Copy": {"value": [{"hello": "there"}, {"is": {"legally": "variable_value", "mixed": [{"name": "<PERSON>"}, true, 38, "<PERSON>"], "num": 1000}, "my": 1, "name": true}, {"authenticated": true}]}}}, "text/plain": {"examples": {"PUT Request": {"value": "This is expected to be sent back as part of response body."}}}}}, "responses": {"200": {"description": ""}}}}, "/patch": {"patch": {"tags": ["Request Methods"], "summary": "PATCH Request", "description": "The HTTP `PATCH` method is used to update resources on a server. The exact\nuse of `PATCH` requests depends on the server in question. There are a number\nof server implementations which handle `PATCH` differently. Technically, \n`PATCH` supports both Query String parameters and a Request Body.\n\nThis endpoint accepts an HTTP `PATCH` request and provides debug information\nsuch as the HTTP headers, Query String arguments, and the Request Body.", "operationId": "patchRequest", "requestBody": {"content": {"text/plain": {"examples": {"PATCH Request": {"value": "This is expected to be sent back as part of response body."}}}}}, "responses": {"200": {"description": ""}}}}, "/delete": {"delete": {"tags": ["Request Methods"], "summary": "DELETE Request", "description": "The HTTP `DELETE` method is used to delete resources on a server. The exact\nuse of `DELETE` requests depends on the server implementation. In general, \n`DELETE` requests support both, Query String parameters as well as a Request \nBody.\n\nThis endpoint accepts an HTTP `DELETE` request and provides debug information\nsuch as the HTTP headers, Query String arguments, and the Request Body.", "operationId": "deleteRequest", "requestBody": {"content": {"text/plain": {"examples": {"DELETE Request": {"value": "This is expected to be sent back as part of response body."}}}}}, "responses": {"200": {"description": ""}}}}, "/headers": {"get": {"tags": ["Headers"], "summary": "Request Headers", "description": "A `GET` request to this endpoint returns the list of all request headers as part of the response JSON.\nIn Postman, sending your own set of headers through the [Headers tab](https://www.getpostman.com/docs/requests#headers?source=echo-collection-app-onboarding) will reveal the headers as part of the response.", "operationId": "requestHeaders", "parameters": [{"name": "my-sample-header", "in": "header", "schema": {"type": "string", "example": "Lorem ipsum dolor sit amet"}, "description": "My Sample Header"}], "responses": {"200": {"description": "my-sample-header", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "342"}}, "Date": {"schema": {"type": "string", "example": "Thu, 31 Mar 2016 11:14:00 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"headers": {"type": "object", "properties": {"accept": {"type": "string", "example": "*/*"}, "accept-encoding": {"type": "string", "example": "gzip, deflate, sdch"}, "accept-language": {"type": "string", "example": "en-US,en;q=0.8"}, "cache-control": {"type": "string", "example": "no-cache"}, "host": {"type": "string", "example": "echo.getpostman.com"}, "my-sample-header": {"type": "string", "example": "Lorem ipsum dolor sit amet"}, "postman-token": {"type": "string", "example": "3c8ea80b-f599-fba6-e0b4-a0910440e7b6"}, "user-agent": {"type": "string", "example": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.110 Safari/537.36"}, "x-forwarded-port": {"type": "string", "example": "443"}, "x-forwarded-proto": {"type": "string", "example": "https"}}}}}, "examples": {"my-sample-header": {"value": {"headers": {"accept": "*/*", "accept-encoding": "gzip, deflate, sdch", "accept-language": "en-US,en;q=0.8", "cache-control": "no-cache", "host": "echo.getpostman.com", "my-sample-header": "Lorem ipsum dolor sit amet", "postman-token": "3c8ea80b-f599-fba6-e0b4-a0910440e7b6", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.110 Safari/537.36", "x-forwarded-port": "443", "x-forwarded-proto": "https"}}}}}}}}}}, "/response-headers": {"get": {"tags": ["Headers"], "summary": "Response Headers", "description": "This endpoint causes the server to send custom set of response headers. Providing header values as part of the URL parameters of a `GET` request to this endpoint returns the same as part of response header.\n\nTo send your own set of headers, simply add or replace the the URL parameters with your own set.", "operationId": "responseHeaders", "parameters": [{"name": "foo1", "in": "query", "schema": {"type": "string", "example": "bar1"}}, {"name": "foo2", "in": "query", "schema": {"type": "string", "example": "bar2"}}], "responses": {"200": {"description": "Response headers", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "71"}}, "Date": {"schema": {"type": "string", "example": "Thu, 31 Mar 2016 11:14:18 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}, "test": {"schema": {"type": "string", "example": "response_headers"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"Content-Type": {"type": "string", "example": "text/html"}, "test": {"type": "string", "example": "response_headers"}}}, "examples": {"Response headers": {"value": {"Content-Type": "text/html", "test": "response_headers"}}}}}}}}}, "/basic-auth": {"get": {"tags": ["Authentication Methods"], "summary": "Basic Auth", "description": "This endpoint simulates a **basic-auth** protected endpoint. \nThe endpoint accepts a default username and password and returns a status code of `200 ok` only if the same is provided. \nOtherwise it will return a status code `401 unauthorized`.\n\n> Username: `postman`\n> \n> Password: `password`\n\nTo use this endpoint, send a request with the header `Authorization: Basic cG9zdG1hbjpwYXNzd29yZA==`. \nThe cryptic latter half of the header value is a base64 encoded concatenation of the default username and password. \nUsing Postman, to send this request, you can simply fill in the username and password in the \"Authorization\" tab and <PERSON>man will do the rest for you.\n\nTo know more about basic authentication, refer to the [Basic Access Authentication](https://en.wikipedia.org/wiki/Basic_access_authentication) wikipedia article.\nThe article on [authentication helpers](https://www.getpostman.com/docs/helpers#basic-auth?source=echo-collection-app-onboarding) elaborates how to use the same within the Postman app.", "operationId": "basicAuth", "responses": {"200": {"description": "200", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "42"}}, "Date": {"schema": {"type": "string", "example": "Sat, 31 Oct 2015 06:38:25 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"authenticated": {"type": "boolean", "example": true}}}, "examples": {"200": {"value": {"authenticated": true}}}}}}}, "security": [{"basicAuth": []}]}}, "/digest-auth": {"get": {"tags": ["Auth: Digest"], "summary": "DigestAuth Success", "description": "This endpoint sends a hashed Digest Authorization header to gain access to a valid `200 Ok` response code. In Postman, it uses the stored [global variables](https://www.getpostman.com/docs/environments#gloval-variables?source=echo-collection-app-onboarding), `echo_digest_realm` and `echo_digest_nonce`, to generate the hashed authorisation header.\n\nWithin Postman, for this request to successfully authenticate, running the previous request \"DigestAuth Request\" stores the relevant information within the global variables.", "operationId": "digestauthSuccess", "responses": {"200": {"description": "200", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "42"}}, "Date": {"schema": {"type": "string", "example": "Thu, 29 Oct 2015 06:17:51 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"authenticated": {"type": "boolean", "example": true}}}, "examples": {"200": {"value": {"authenticated": true}}}}}}}, "security": [{"digestAuth": []}]}}, "/auth/hawk": {"get": {"tags": ["Authentication Methods"], "summary": "<PERSON>", "description": "This endpoint is a Hawk Authentication protected endpoint. [Hawk authentication](https://github.com/hueniverse/hawk) is a widely used protocol for protecting API endpoints. One of Hawk's main goals is to enable HTTP authentication for services that do not use TLS (although it can be used in conjunction with TLS as well).\n\nIn order to use this endpoint, select the \"Hawk Auth\" helper inside Postman, and set the following values:\n\nHawk Auth ID: `dh37fgj492je`\n\nHawk Auth Key: `werxhqb98rpaxn39848xrunpaw3489ruxnpa98w4rxn`\n\nAlgorithm: `sha256`\n\nThe rest of the values are optional, and can be left blank. Hitting send should give you a response with a status code of 200 OK.", "operationId": "hawkAuth", "responses": {"200": {"description": "Success", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Date": {"schema": {"type": "string", "example": "Thu, 31 Mar 2016 11:12:16 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Server-Authorization": {"schema": {"type": "string", "example": "Hawk mac=\"vRrUzDdcHu2NaNts/r4zg2xmXMdX8wPiTGTM398BDRg=\", hash=\"qmtflETMybaZiOQ2dLT17yiRunFT5OCIxZRZ0boQaiE=\""}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}, "transfer-encoding": {"schema": {"type": "string", "example": "chunked"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Hawk Authentication successful"}, "status": {"type": "string", "example": "pass"}}}, "examples": {"Success": {"value": {"message": "Hawk Authentication successful", "status": "pass"}}}}}}}}}, "/oauth1": {"get": {"tags": ["Authentication Methods"], "summary": "OAuth1.0 (verify signature)", "description": "OAuth1.0a is a specification that defines a protocol that can be used by one\nservice to access \"protected\" resources (endpoints) on another service. A\nmajor part of OAuth1.0 is HTTP Request Signing. This endpoint allows you to \ncheck whether the request calculation works properly in the client. \n\nThe endpoint supports the HTTP ``Authorization`` header. In case the signature\nverification fails, the endpoint provides the four debug values,\n\n* ``base_uri``\n* ``normalized_param_string``\n* ``base_string``\n* ``signing_key``\n\nFor more details about these parameters, check the [OAuth1.0a Specification](http://oauth.net/core/1.0a/)\n\nIn order to use this endpoint, you can set the following values:\n\n> Consumer Key: ``RKCGzna7bv9YD57c``\n>\n> Consumer Secret: ``D+EdQ-gs$-%@2Nu7``\n\nIf you are using Postman, also check the \"Add params to header\" and \n\"Auto add parameters\" boxes.", "operationId": "oauth10VerifySignature", "responses": {"200": {"description": "200", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "95"}}, "Date": {"schema": {"type": "string", "example": "Thu, 25 Aug 2016 10:34:23 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"4e-Cq3UhvpVSyl6R6204lPVIA\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.8.1"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "OAuth-1.0a signature verification was successful"}, "status": {"type": "string", "example": "pass"}}}, "examples": {"200": {"value": {"message": "OAuth-1.0a signature verification was successful", "status": "pass"}}}}}}, "401": {"description": "401", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Length": {"schema": {"type": "string", "example": "536"}}, "Date": {"schema": {"type": "string", "example": "Thu, 25 Aug 2016 10:34:55 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"218-SGnurnTsu5qV5cCYWxsJlg\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.8.1"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"base_string": {"type": "string", "example": "GET&https%3A%2F%2Fecho.getpostman.com%2Foauth1&oauth_consumer_key%3DRKCGzna7bv9YD57c_wrong%26oauth_nonce%3D8LTsU2%26oauth_signature_method%3DHMAC-SHA1%26oauth_timestamp%3D1472121295%26oauth_version%3D1.0"}, "base_uri": {"type": "string", "example": "https://echo.getpostman.com/oauth1"}, "message": {"type": "string", "example": "HMAC-SHA1 verification failed"}, "normalized_param_string": {"type": "string", "example": "oauth_consumer_key=RKCGzna7bv9YD57c_wrong&oauth_nonce=8LTsU2&oauth_signature_method=HMAC-SHA1&oauth_timestamp=1472121295&oauth_version=1.0"}, "signing_key": {"type": "string", "example": "D%2BEdQ-gs%24-%25%402Nu7&"}, "status": {"type": "string", "example": "fail"}}}, "examples": {"401": {"value": {"base_string": "GET&https%3A%2F%2Fecho.getpostman.com%2Foauth1&oauth_consumer_key%3DRKCGzna7bv9YD57c_wrong%26oauth_nonce%3D8LTsU2%26oauth_signature_method%3DHMAC-SHA1%26oauth_timestamp%3D1472121295%26oauth_version%3D1.0", "base_uri": "https://echo.getpostman.com/oauth1", "message": "HMAC-SHA1 verification failed", "normalized_param_string": "oauth_consumer_key=RKCGzna7bv9YD57c_wrong&oauth_nonce=8LTsU2&oauth_signature_method=HMAC-SHA1&oauth_timestamp=1472121295&oauth_version=1.0", "signing_key": "D%2BEdQ-gs%24-%25%402Nu7&", "status": "fail"}}}}}}}}}, "/cookies/set": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Set Cookies", "description": "The cookie setter endpoint accepts a list of cookies and their values as part of URL parameters of a `GET` request. These cookies are saved and can be subsequently retrieved or deleted. The response of this request returns a JSON with all cookies listed.\n\nTo set your own set of cookies, simply replace the URL parameters \"foo1=bar1&foo2=bar2\" with your own set of key-value pairs.", "operationId": "setCookies", "parameters": [{"name": "foo1", "in": "query", "schema": {"type": "string", "example": "bar1"}}, {"name": "foo2", "in": "query", "schema": {"type": "string", "example": "bar2"}}], "responses": {"200": {"description": "Cookies", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "51"}}, "Date": {"schema": {"type": "string", "example": "Thu, 29 Oct 2015 06:15:28 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"cookies": {"type": "object", "properties": {"foo1": {"type": "string", "example": "bar"}, "foo2": {"type": "string", "example": "bar"}}}}}, "examples": {"Cookies": {"value": {"cookies": {"foo1": "bar", "foo2": "bar"}}}}}}}}}}, "/cookies": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Get Cookies", "description": "Use this endpoint to get a list of all cookies that are stored with respect to this domain. Whatever key-value pairs that has been previously set by calling the \"Set Cookies\" endpoint, will be returned as response JSON.", "operationId": "getCookies", "responses": {"200": {"description": "Cookies", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "46"}}, "Date": {"schema": {"type": "string", "example": "Thu, 29 Oct 2015 06:16:29 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"cookies": {"type": "object", "properties": {"foo2": {"type": "string", "example": "bar"}}}}}, "examples": {"Cookies": {"value": {"cookies": {"foo2": "bar"}}}}}}}}}}, "/cookies/delete": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Delete Cookies", "description": "One or more cookies that has been set for this domain can be deleted by providing the cookie names as part of the URL parameter. The response of this request is a JSON containing the list of currently set cookies.", "operationId": "deleteCookies", "parameters": [{"name": "foo1", "in": "query", "schema": {"type": "string"}}, {"name": "foo2", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Cookies Response", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "46"}}, "Date": {"schema": {"type": "string", "example": "Thu, 29 Oct 2015 06:16:00 GMT"}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"cookies": {"type": "object", "properties": {"foo2": {"type": "string", "example": "bar"}}}}}, "examples": {"Cookies Response": {"value": {"cookies": {"foo2": "bar"}}}}}}}}}}, "/status/200": {"get": {"tags": ["Utilities"], "summary": "Response Status Code", "description": "This endpoint allows one to instruct the server which status code to respond with.\n\nEvery response is accompanied by a status code. The status code provides a summary of the nature of response sent by the server. For example, a status code of `200` means everything is okay with the response and a code of `404` implies that the requested URL does not exist on server. \nA list of all valid HTTP status code can be found at the [List of Status Codes](https://en.wikipedia.org/wiki/List_of_HTTP_status_codes) wikipedia article. When using Postman, the response status code is described for easy reference.\n\nNote that if an invalid status code is requested to be sent, the server returns a status code of `400 Bad Request`.", "operationId": "responseStatusCode", "responses": {"200": {"description": "200", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Length": {"schema": {"type": "string", "example": "14"}}, "Date": {"schema": {"type": "string", "example": "Thu, 31 Mar 2016 11:58:47 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"e-1056260003\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.6.2"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "X-Powered-By": {"schema": {"type": "string", "example": "Sails <sailsjs.org>"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "number", "example": 200}}}, "examples": {"200": {"value": {"status": 200}}}}}}}}}, "/stream/5": {"get": {"tags": ["Utilities"], "summary": "Streamed Response", "description": "This endpoint allows one to recieve streaming http response using [chunked transfer encoding](https://en.wikipedia.org/wiki/Chunked_transfer_encoding) of a configurable length.\n\nA streaming response does not wait for the entire response to be generated on server before flushing it out. This implies that for a fairly large response, parts of it can be streamed to the requestee as and when it is generated on server. The client can then take actions of processing this partially received data.", "operationId": "streamedResponse", "responses": {"200": {"description": ""}}}}, "/delay/2": {"get": {"tags": ["Utilities"], "summary": "Delay Response", "description": "Using this endpoint one can configure how long it takes for the server to come back with a response. Appending a number to the URL defines the time (in seconds) the server will wait before responding.\n\nNote that a maximum delay of 10 seconds is accepted by the server.", "operationId": "delayResponse", "responses": {"200": {"description": "success-response", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Length": {"schema": {"type": "string", "example": "13"}}, "Date": {"schema": {"type": "string", "example": "Mon, 02 Jan 2017 09:19:03 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"d-t/L/D5c0SDl+MoXtKdSVOg\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.10.1"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"delay": {"type": "string", "example": "3"}}}, "examples": {"success-response": {"value": {"delay": "3"}}}}}}}}}, "/encoding/utf8": {"get": {"tags": ["Utilities"], "summary": "Get UTF8 Encoded Response", "description": "If a response of an endpoint requires to send data beyond the basic English / ASCII character set, the `charset` parameter in the `Content-Type` response header defines the character encoding policy.\n\nThis endpoint returns an `UTF8` character encoded response body with text in various languages such as Greek, Latin, East Asian, etc. <PERSON><PERSON> can interpret the character encoding and use appropriate methods to display the character set in responses.", "operationId": "getUtf8EncodedResponse", "responses": {"200": {"description": ""}}}}, "/gzip": {"get": {"tags": ["Utilities"], "summary": "GZip Compressed Response", "description": "This endpoint returns the response using [gzip compression algoritm](https://en.wikipedia.org/wiki/Gzip).\nThe uncompressed response is a JSON string containing the details of the request sent by the client. For this endpoint to work, one should request with `Accept-encoding` header containing `gzip` as part of its value. <PERSON><PERSON> supports gzip, deflate and SDCH decoding and automatically sends them as part of the request.\n\nHTTP Compression allows the server to send responses in a compressed format, which is uncompressed by the client before processing. This reduces network bandwidth consumption at the cost of increase in CPU usage.\nTo know more about this, refer the [HTTP Compression](https://en.wikipedia.org/wiki/HTTP_compression) wikipedia article.", "operationId": "gzipCompressedResponse", "responses": {"200": {"description": ""}}}}, "/deflate": {"get": {"tags": ["Utilities"], "summary": "Deflate Compressed Response", "description": "This endpoint returns the response using [deflate compression algoritm](https://en.wikipedia.org/wiki/DEFLATE). \nThe uncompressed response is a JSON string containing the details of the request sent by the client. For this endpoint to work, one should request with `Accept-encoding` header containing `deflate` as part of its value. <PERSON><PERSON> supports gzip, deflate and SDCH decoding and automatically sends them as part of the request.\n\nHTTP Compression allows the server to send responses in a compressed format, which is uncompressed by the client before processing. This reduces network bandwidth consumption at the cost of increase in CPU usage.\nTo know more about this, refer the [HTTP Compression](https://en.wikipedia.org/wiki/HTTP_compression) wikipedia article.", "operationId": "deflateCompressedResponse", "responses": {"200": {"description": ""}}}}, "/ip": {"get": {"tags": ["Utilities"], "summary": "IP address in JSON format", "description": "A simple `GET` request to return the IP address of the source request in the following `JSON` format:\n\n```json\n{\n  ip: \"request-ip-address\"\n}\n```", "operationId": "ipAddressInJsonFormat", "responses": {"200": {"description": ""}}}}, "/time/now": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Current UTC time", "description": "A simple `GET` request to `/time/now` to return the current timestamp as a UTC string.\n\n```\nFri, 04 Nov 2016 09:00:46 GMT\n```", "operationId": "currentUtcTime", "responses": {"200": {"description": "time as text", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Content-Length": {"schema": {"type": "string", "example": "49"}}, "Date": {"schema": {"type": "string", "example": "We<PERSON>, 11 Jan 2017 10:27:12 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"1d-2jJhkzratfVX9VZ0+raHbw\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.10.1"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "set-cookie": {"schema": {"type": "string", "example": "sails.sid=s%3A2lT3TO7qS1tadeSAp4axl-NcXG9CV6Rf.HGqLY%2FlKEKY4fgCLePaAZs3tCHp%2Bglf7ZOJYlonGeig; Path=/; HttpOnly"}}}, "content": {"text/plain": {"examples": {"time as text": {"value": "We<PERSON>, 11 Jan 2017 10:27:12 GMT"}}}}}}}}, "/time/valid": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Timestamp validity", "description": "A simple `GET` request to `/time/valid` to determine the validity of the timestamp, (current by default).\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a valid key to indicate the result. The response code is `200`.\n\n```\n{\n  valid: true/false\n}\n```", "operationId": "timestampValidity", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}], "responses": {"200": {"description": "Valid Timestamp / Invalid Timestamp", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Length": {"schema": {"type": "string", "example": "15"}}, "Date": {"schema": {"type": "string", "example": "We<PERSON>, 11 Jan 2017 10:27:53 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"f-/i9mO/upK91ZtL0BkKFGtw\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.10.1"}}, "Vary": {"schema": {"type": "string", "example": "Accept-Encoding"}}, "set-cookie": {"schema": {"type": "string", "example": "sails.sid=s%3ATNJaNxi2QCv4RPBb64sIZxQGN1h6IP3g.9sQVAijlsLsh0r7LgffxXa9k2we6UumPEVv%2Bsk4woLI; Path=/; HttpOnly"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"valid": {"type": "boolean", "example": true}}}, "examples": {"Invalid Timestamp": {"value": {"valid": false}}, "Valid Timestamp": {"value": {"valid": true}}}}}}}}}, "/time/format": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Format timestamp", "description": "A simple `GET` request to `/time/format` to convert the timestamp to any desired valid format.\n\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `format` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  format: \"formatted-timestamp\"\n}\n```", "operationId": "formatTimestamp", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "format", "in": "query", "schema": {"type": "string", "example": "mm"}}], "responses": {"200": {"description": ""}}}}, "/time/unit": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Extract timestamp unit", "description": "A simple `GET` request to `/time/unit` to extract the specified timestamp unit (as provided in the `unit` query parameter). The default unit returned is the `year`.\n\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `unit` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  unit: \"extracted-timestamp-unit\"\n}\n```", "operationId": "extractTimestampUnit", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "unit", "in": "query", "schema": {"type": "string", "example": "day"}}], "responses": {"200": {"description": ""}}}}, "/time/add": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Time addition", "description": "A simple `GET` request to `/time/add` to add units of time to the specified / current timestamp (as provided in the `years`, `months`, `days`, `hours`, `minutes`, `seconds`, and `milliseconds` query parameters).\n\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `sum` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  sum: \"sum of (provided / current) and provided timestamps\"\n}\n```", "operationId": "timeAddition", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "years", "in": "query", "schema": {"type": "string", "example": "100"}}], "responses": {"200": {"description": ""}}}}, "/time/subtract": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Time subtraction", "description": "A simple `GET` request to `/time/subtract` to subtract units of time from the specified / current timestamp (as provided in the `years`, `months`, `days`, `hours`, `minutes`, `seconds`, and `milliseconds` query parameters).\n\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `difference` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  difference: \"difference between (provided / current) and provided timestamps\"\n}\n```", "operationId": "timeSubtraction", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "years", "in": "query", "schema": {"type": "string", "example": "50"}}], "responses": {"200": {"description": ""}}}}, "/time/start": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Start of time", "description": "A simple `GET` request to `/time/start` to return a relative timstamp in the past from the specified / current timestamp (as provided in the `unit` query parameter).\n\nFor instance, if the `unit` has been specified as `month`, the returned timestamp would indicate the beginning of the current month. Similar results are returned for other units of time, like: `years`, `months`, `days`, `hours`, `minutes`, `seconds`, and `milliseconds`\n\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `start` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  start: \"A timestamp from the past, depending on the `unit` specified\"\n}\n```", "operationId": "startOfTime", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "unit", "in": "query", "schema": {"type": "string", "example": "month"}}], "responses": {"200": {"description": ""}}}}, "/time/object": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Object representation", "description": "A simple `GET` request to `/time/object` to return the current / provided timestamp as a JSON object.\n\nFor instance, if the `unit` has been specified as `month`, the returned timestamp would indicate the beginning of the current month. Similar results are returned for other units of time, like: `years`, `months`, `days`, `hours`, `minutes`, `seconds`, and `milliseconds`\n\nThis endpoint accepts `timestamp`, `locale`, `format`, and `strict` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  years: 2016,\n  months: 10,\n  days: 10,\n  hours: 23,\n  minutes: 34,\n  seconds: 20,\n  milliseconds: 980\n}\n```", "operationId": "objectRepresentation", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}], "responses": {"200": {"description": ""}}}}, "/time/before": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Before comparisons", "description": "A simple `GET` request to `/time/before` to check if the provided timestamps is before a comparison `target` (query parameter).\n\nThis endpoint accepts `timestamp`, `locale`, `format`, `strict`, and `target` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `before` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  before: true/false\n}\n```", "operationId": "beforeComparisons", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "target", "in": "query", "schema": {"type": "string", "example": "2017-10-10"}}], "responses": {"200": {"description": ""}}}}, "/time/after": {"get": {"tags": ["Utilities / Date and Time"], "summary": "After comparisons", "description": "A simple `GET` request to `/time/after` to check if the provided timestamps is after a comparison `target` (query parameter).\n\nThis endpoint accepts `timestamp`, `locale`, `format`, `strict`, and `target` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `after` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  after: true/false\n}\n```", "operationId": "afterComparisons", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "target", "in": "query", "schema": {"type": "string", "example": "2017-10-10"}}], "responses": {"200": {"description": ""}}}}, "/time/between": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Between timestamps", "description": "A simple `GET` request to `/time/between` to check if the provided timestamp is between a range specified by the `start` and `end` query parameters. A resolution limit can also be specified by the `unit` query parameter.\n\nFor instance, for a resolution `unit` of `month`,\n`2016-10-05` does lie between `2016-11-02` and `2016-09-01`.\n\nThis endpoint also accepts `timestamp`, `locale`, `format`, `strict`, and `target` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `between` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  between: true/false\n}\n```", "operationId": "betweenTimestamps", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}, {"name": "start", "in": "query", "schema": {"type": "string", "example": "2017-10-10"}}, {"name": "end", "in": "query", "schema": {"type": "string", "example": "2019-10-10"}}], "responses": {"200": {"description": ""}}}}, "/time/leap": {"get": {"tags": ["Utilities / Date and Time"], "summary": "Leap year check", "description": "A simple `GET` request to `/time/leap` to check if the provided/current timestamp belongs to a leap year.\n\nThis endpoint also accepts `timestamp`, `locale`, `format`, `strict`, and `target` query parameters to construct the date time instance to check against.\n\nResponses are provided in JSON format, with a `leap` key to indicate the result. The response code is `200` for valid query parameters, and `400` otherwise.\n\n```\n{\n  leap: true/false\n}\n```", "operationId": "leapYear<PERSON>heck", "parameters": [{"name": "timestamp", "in": "query", "schema": {"type": "string", "example": "2016-10-10"}}], "responses": {"200": {"description": ""}}}}, "/transform/collection": {"post": {"tags": ["Utilities / Postman Collection"], "summary": "Transform collection from format v1 to v2", "description": "Transform collection from format v1 to v2", "operationId": "transformCollectionFromFormatV1ToV2", "parameters": [{"name": "from", "in": "query", "schema": {"type": "string", "example": "1"}}, {"name": "to", "in": "query", "schema": {"type": "string", "example": "2"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"info": {"type": "object", "properties": {"description": {"type": "string", "example": "A sample collection to demonstrate collections as a set of related requests"}, "name": {"type": "string", "example": "Sample Postman Collection"}, "schema": {"type": "string", "example": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}}}, "item": {"type": "array", "items": {"type": "object", "properties": {"event": {"type": "array", "items": {"type": "object", "properties": {"listen": {"type": "string", "example": "test"}, "script": {"type": "object", "properties": {"exec": {"type": "array", "items": {"type": "string", "example": "tests['response code is 200'] = (responseCode.code === 200);"}, "example": ["tests['response code is 200'] = (responseCode.code === 200);"]}, "type": {"type": "string", "example": "text/javascript"}}}}}, "example": [{"listen": "test", "script": {"exec": ["tests['response code is 200'] = (responseCode.code === 200);"], "type": "text/javascript"}}]}, "name": {"type": "string", "example": "A simple GET request"}, "request": {"type": "object", "properties": {"body": {"type": "object", "properties": {"mode": {"type": "string", "example": "raw"}, "raw": {"type": "string", "example": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium..."}}}, "header": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "example": "Content-Type"}, "value": {"type": "string", "example": "text/plain"}}}, "example": [{"key": "Content-Type", "value": "text/plain"}]}, "method": {"type": "string", "example": "GET"}, "url": {"type": "string", "example": "https://postman-echo.com/get?source=newman-sample-github-collection"}}}}}, "example": [{"event": [{"listen": "test", "script": {"exec": ["tests['response code is 200'] = (responseCode.code === 200);"], "type": "text/javascript"}}], "name": "A simple GET request", "request": {"method": "GET", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}}, {"name": "A simple POST request", "request": {"body": {"mode": "raw", "raw": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium..."}, "header": [{"key": "Content-Type", "value": "text/plain"}], "method": "POST", "url": "https://postman-echo.com/post"}}, {"name": "A simple POST request with JSON body", "request": {"body": {"mode": "raw", "raw": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}"}, "header": [{"key": "Content-Type", "value": "application/json"}], "method": "POST", "url": "https://postman-echo.com/post"}}]}}}, "examples": {"Transform collection from format v1 to v2": {"value": {"description": "A sample collection to demonstrate collections as a set of related requests", "folders": [], "id": "7875be4b-917d-4aff-8cc4-5606c36bf418", "name": "Sample Postman Collection", "order": ["4d9134be-e8bf-4693-9cd7-1c0fc66ae739", "141ba274-cc50-4377-a59c-e080066f375e", "4511ca8b-0bc7-430f-b894-a7ec1036f322"], "requests": [{"collectionId": "877b9dae-a50e-4152-9b89-870c37216f78", "data": [], "headers": "", "id": "4d9134be-e8bf-4693-9cd7-1c0fc66ae739", "method": "GET", "name": "A simple GET request", "preRequestScript": "", "rawModeData": "", "tests": "tests['response code is 200'] = (responseCode.code === 200);", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}, {"collectionId": "877b9dae-a50e-4152-9b89-870c37216f78", "data": [], "dataMode": "raw", "headers": "Content-Type: text/plain", "id": "141ba274-cc50-4377-a59c-e080066f375e", "method": "POST", "name": "A simple POST request", "rawModeData": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...", "url": "https://postman-echo.com/post"}, {"collectionId": "877b9dae-a50e-4152-9b89-870c37216f78", "data": [], "dataMode": "raw", "headers": "Content-Type: application/json", "id": "4511ca8b-0bc7-430f-b894-a7ec1036f322", "method": "POST", "name": "A simple POST request with JSON body", "rawModeData": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}", "url": "https://postman-echo.com/post"}]}}, "Transform collection from format v2 to v1": {"value": {"info": {"description": "A sample collection to demonstrate collections as a set of related requests", "name": "Sample Postman Collection", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"event": [{"listen": "test", "script": {"exec": ["tests['response code is 200'] = (responseCode.code === 200);"], "type": "text/javascript"}}], "name": "A simple GET request", "request": {"method": "GET", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}}, {"name": "A simple POST request", "request": {"body": {"mode": "raw", "raw": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium..."}, "header": [{"key": "Content-Type", "value": "text/plain"}], "method": "POST", "url": "https://postman-echo.com/post"}}, {"name": "A simple POST request with JSON body", "request": {"body": {"mode": "raw", "raw": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}"}, "header": [{"key": "Content-Type", "value": "application/json"}], "method": "POST", "url": "https://postman-echo.com/post"}}]}}}}}}, "responses": {"200": {"description": "Sample v2 Response / Sample v1 Response", "headers": {"Connection": {"schema": {"type": "string", "example": "keep-alive"}}, "Content-Encoding": {"schema": {"type": "string", "example": "gzip"}}, "Date": {"schema": {"type": "string", "example": "We<PERSON>, 11 Jan 2017 10:38:42 GMT"}}, "ETag": {"schema": {"type": "string", "example": "W/\"569-P9uLZEIyoPfMmQ+U0mTO1A\""}}, "Server": {"schema": {"type": "string", "example": "nginx/1.10.1"}}, "Vary": {"schema": {"type": "string", "example": "X-HTTP-Method-Override, Accept-Encoding"}}, "set-cookie": {"schema": {"type": "string", "example": "sails.sid=s%3A55y5Ll7HpTzt_hKuw6N54k4N04ilmMdn.uCPCHttP5DmI%2BdBw2I9NZL55lFFOzz4XxS4qAHv47gI; Path=/; HttpOnly"}}, "transfer-encoding": {"schema": {"type": "string", "example": "chunked"}}}, "content": {"application/json": {"schema": {"type": "object", "properties": {"description": {"type": "string", "example": "A sample collection to demonstrate collections as a set of related requests"}, "folders": {"type": "array", "items": {}, "example": []}, "id": {"type": "string", "example": "0c42230c-c8e4-4ca0-a4aa-d393971de8b8"}, "info": {"type": "object", "properties": {"_postman_id": {"type": "string", "example": "7875be4b-917d-4aff-8cc4-5606c36bf418"}, "description": {"type": "string", "example": "A sample collection to demonstrate collections as a set of related requests"}, "name": {"type": "string", "example": "Sample Postman Collection"}, "schema": {"type": "string", "example": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}}}, "item": {"type": "array", "items": {"type": "object", "properties": {"event": {"type": "array", "items": {"type": "object", "properties": {"listen": {"type": "string", "example": "test"}, "script": {"type": "object", "properties": {"exec": {"type": "array", "items": {"type": "string", "example": "tests['response code is 200'] = (responseCode.code === 200);"}, "example": ["tests['response code is 200'] = (responseCode.code === 200);"]}, "type": {"type": "string", "example": "text/javascript"}}}}}, "example": [{"listen": "test", "script": {"exec": ["tests['response code is 200'] = (responseCode.code === 200);"], "type": "text/javascript"}}]}, "name": {"type": "string", "example": "A simple GET request"}, "request": {"type": "object", "properties": {"body": {"type": "object", "properties": {"mode": {"type": "string", "example": "raw"}, "raw": {"type": "string", "example": ""}}}, "header": {"type": "array", "items": {}, "example": []}, "method": {"type": "string", "example": "GET"}, "url": {"type": "string", "example": "https://postman-echo.com/get?source=newman-sample-github-collection"}}}, "response": {"type": "array", "items": {}, "example": []}}}, "example": [{"event": [{"listen": "test", "script": {"exec": ["tests['response code is 200'] = (responseCode.code === 200);"], "type": "text/javascript"}}], "name": "A simple GET request", "request": {"body": {"mode": "raw", "raw": ""}, "header": [], "method": "GET", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}, "response": []}, {"name": "A simple POST request", "request": {"body": {"mode": "raw", "raw": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium..."}, "header": [{"description": "", "key": "Content-Type", "value": "text/plain"}], "method": "POST", "url": "https://postman-echo.com/post"}, "response": []}, {"name": "A simple POST request with JSON body", "request": {"body": {"mode": "raw", "raw": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}"}, "header": [{"description": "", "key": "Content-Type", "value": "application/json"}], "method": "POST", "url": "https://postman-echo.com/post"}, "response": []}]}, "name": {"type": "string", "example": "Sample Postman Collection"}, "order": {"type": "array", "items": {"type": "string", "example": "3d04ed83-dc1e-40ec-923c-16aa92509e50"}, "example": ["3d04ed83-dc1e-40ec-923c-16aa92509e50", "e02f8160-fb41-4633-be80-cc7d701e85b4", "77bd6d4d-1060-4927-aa5c-dcdba7f750cf"]}, "requests": {"type": "array", "items": {"type": "object", "properties": {"collectionId": {"type": "string", "example": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9"}, "data": {"type": "array", "items": {}, "example": []}, "dataMode": {"type": "string", "example": "raw"}, "headers": {"type": "string", "example": ""}, "id": {"type": "string", "example": "3d04ed83-dc1e-40ec-923c-16aa92509e50"}, "method": {"type": "string", "example": "GET"}, "name": {"type": "string", "example": "A simple GET request"}, "preRequestScript": {"type": "string", "example": ""}, "rawModeData": {"type": "string", "example": ""}, "tests": {"type": "string", "example": "tests['response code is 200'] = (responseCode.code === 200);"}, "url": {"type": "string", "example": "https://postman-echo.com/get?source=newman-sample-github-collection"}}}, "example": [{"collectionId": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9", "data": [], "headers": "", "id": "3d04ed83-dc1e-40ec-923c-16aa92509e50", "method": "GET", "name": "A simple GET request", "preRequestScript": "", "rawModeData": "", "tests": "tests['response code is 200'] = (responseCode.code === 200);", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}, {"collectionId": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9", "data": [], "dataMode": "raw", "headers": "Content-Type: text/plain", "id": "e02f8160-fb41-4633-be80-cc7d701e85b4", "method": "POST", "name": "A simple POST request", "rawModeData": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...", "url": "https://postman-echo.com/post"}, {"collectionId": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9", "data": [], "dataMode": "raw", "headers": "Content-Type: application/json", "id": "77bd6d4d-1060-4927-aa5c-dcdba7f750cf", "method": "POST", "name": "A simple POST request with JSON body", "rawModeData": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}", "url": "https://postman-echo.com/post"}]}, "variables": {"type": "array", "items": {}, "example": []}}}, "examples": {"Sample v1 Response": {"value": {"description": "A sample collection to demonstrate collections as a set of related requests", "folders": [], "id": "0c42230c-c8e4-4ca0-a4aa-d393971de8b8", "name": "Sample Postman Collection", "order": ["3d04ed83-dc1e-40ec-923c-16aa92509e50", "e02f8160-fb41-4633-be80-cc7d701e85b4", "77bd6d4d-1060-4927-aa5c-dcdba7f750cf"], "requests": [{"collectionId": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9", "data": [], "headers": "", "id": "3d04ed83-dc1e-40ec-923c-16aa92509e50", "method": "GET", "name": "A simple GET request", "preRequestScript": "", "rawModeData": "", "tests": "tests['response code is 200'] = (responseCode.code === 200);", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}, {"collectionId": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9", "data": [], "dataMode": "raw", "headers": "Content-Type: text/plain", "id": "e02f8160-fb41-4633-be80-cc7d701e85b4", "method": "POST", "name": "A simple POST request", "rawModeData": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...", "url": "https://postman-echo.com/post"}, {"collectionId": "1dd68aff-a3fa-4f52-904f-5b75053bc9d9", "data": [], "dataMode": "raw", "headers": "Content-Type: application/json", "id": "77bd6d4d-1060-4927-aa5c-dcdba7f750cf", "method": "POST", "name": "A simple POST request with JSON body", "rawModeData": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}", "url": "https://postman-echo.com/post"}]}}, "Sample v2 Response": {"value": {"info": {"_postman_id": "7875be4b-917d-4aff-8cc4-5606c36bf418", "description": "A sample collection to demonstrate collections as a set of related requests", "name": "Sample Postman Collection", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json"}, "item": [{"event": [{"listen": "test", "script": {"exec": ["tests['response code is 200'] = (responseCode.code === 200);"], "type": "text/javascript"}}], "name": "A simple GET request", "request": {"body": {"mode": "raw", "raw": ""}, "header": [], "method": "GET", "url": "https://postman-echo.com/get?source=newman-sample-github-collection"}, "response": []}, {"name": "A simple POST request", "request": {"body": {"mode": "raw", "raw": "Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium..."}, "header": [{"description": "", "key": "Content-Type", "value": "text/plain"}], "method": "POST", "url": "https://postman-echo.com/post"}, "response": []}, {"name": "A simple POST request with JSON body", "request": {"body": {"mode": "raw", "raw": "{\"text\":\"Duis posuere augue vel cursus pharetra. In luctus a ex nec pretium...\"}"}, "header": [{"description": "", "key": "Content-Type", "value": "application/json"}], "method": "POST", "url": "https://postman-echo.com/post"}, "response": []}], "variables": []}}}}}}}}}, "/{method}/hello": {"get": {"tags": ["Custom"], "summary": "Path variables", "description": "Path variables", "operationId": "pathVariables", "responses": {"200": {"description": ""}}}, "parameters": [{"name": "method", "in": "path", "required": true, "schema": {"type": "string", "example": "get"}, "description": "An HTTP method."}]}}, "components": {"securitySchemes": {"basicAuth": {"type": "http", "scheme": "basic"}, "digestAuth": {"type": "http", "scheme": "digest"}}}, "tags": [{"name": "Request Methods", "description": "HTTP has multiple request \"verbs\", such as `GET`, `PUT`, `POST`, `DELETE`,\n`PATCH`, `HEAD`, etc. \n\nAn HTTP Method (verb) defines how a request should be interpreted by a server. \nThe endpoints in this section demonstrate various HTTP Verbs. <PERSON><PERSON> supports \nall the HTTP Verbs, including some rarely used ones, such as `<PERSON><PERSON>FIND`, `UNLINK`, \netc.\n\nFor details about HTTP Verbs, refer to [RFC 2616](http://www.w3.org/Protocols/rfc2616/rfc2616-sec9.html#sec9)\n"}, {"name": "Headers", "description": "The following set of endpoints allow one to see the headers being sent as part of a request and to get a custom set of headers as part of response.\n\nHTTP header fields provide required information about the request or response, or about the object sent in the message body. Both request headers and response headers can be controlled using these endpoints."}, {"name": "Authentication Methods"}, {"name": "<PERSON><PERSON>", "description": "The cookie related endpoints allow one to get, set and delete simple cookies.\n\nCookies are small snippets of information that is stored in the browser and sent back to the server with every subsequent requests in order to store useful information between requests.\nIf you want to know more about cookies, read the [HTTP Cookie](https://en.wikipedia.org/wiki/HTTP_cookie) article on wikipedia."}, {"name": "Utilities"}, {"name": "Utilities / Date and Time", "description": "A set of `/time/*` mounted requests to perform date-time manipulations, among other operations.\n"}, {"name": "Utilities / Postman Collection"}, {"name": "Auth: Digest", "description": "Digest authentication protects an endpoint with a username and password without actually transmitting the password over network.\nOne has to apply a hash function (like MD5, etc) to the username and password before sending them over the network.\n\n> Username: `postman`\n>\n> Password: `password`\n\nUnlike Basic-Auth, authentication happens using two consecutive requests where the first request returns `401 Unauthorised` along with `WWW-Authenticate` header containing information that needs to be used to authenticate subsequent calls.\n\nTo know more about digest authentication, refer to the [Digest Access Authentication](https://en.wikipedia.org/wiki/Digest_access_authentication) wikipedia article.\nThe article on [authentication helpers](https://www.getpostman.com/docs/helpers#digest-auth) elaborates how to use the same within the Postman app."}, {"name": "Custom"}]}