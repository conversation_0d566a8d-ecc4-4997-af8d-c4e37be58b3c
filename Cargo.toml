[package]
name = "postman2openapi"
description = "Convert a Postman collection to an OpenAPI definition. "
homepage = "https://github.com/kevinswiber/postman2openapi"
repository = "https://github.com/kevinswiber/postman2openapi"
license = "Apache-2.0"
version = "1.2.1"
authors = ["<PERSON> Swiber <<EMAIL>>"]
readme = "README.md"
documentation = "https://docs.rs/postman2openapi"
edition = "2021"
exclude = ["ci/*", "tests/*", "web/*"]

[workspace]
members = ["cli"]

[lib]
name = "postman2openapi"
path = "src/lib.rs"
crate-type = ["cdylib", "rlib"]

[dependencies]
anyhow = "1.0"
convert_case = "0.5.0"
indexmap = { version = "1.5.1", features = ["serde-1"] }
lazy_static = "1.4.0"
regex = { version = "1.6", default-features = false, features = ["std"] }
semver = "1.0.12"
serde = "1.0"
serde_derive = "1.0"
serde_json = { version = "1.0", features = ["raw_value"]}
thiserror = "1.0"

[target.'cfg(not(target_arch = "wasm32"))'.dependencies]
serde_yaml = "0.9"

[target.'cfg(target_arch = "wasm32")'.dependencies]
console_error_panic_hook = { version = "0.1.6", optional = true }
gloo-utils = { version = "0.2", features = ["serde"] }
wasm-bindgen = "0.2"
wee_alloc = { version = "0.4.5", optional = true }

[target.'cfg(target_arch = "wasm32")'.dev-dependencies]
js-sys = "0.3"
wasm-bindgen-test = "0.3.0"

[profile.release]
opt-level = "z"
lto = true

[package.metadata.wasm-pack.profile.release]
wasm-opt = ["-Oz", "--enable-mutable-globals"]
