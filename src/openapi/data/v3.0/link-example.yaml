openapi: 3.0.0
info: 
  title: Link Example
  version: 1.0.0
paths: 
  /2.0/users/{username}: 
    get: 
      operationId: getUserByName
      parameters: 
      - name: username
        in: path
        required: true
        schema:
          type: string
      responses: 
        '200':
          description: The User
          content:
            application/json:
              schema: 
                $ref: '#/components/schemas/user'
          links:
            userRepositories:
              $ref: '#/components/links/UserRepositories'
  /2.0/repositories/{username}:
    get:
      operationId: getRepositoriesByOwner
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: repositories owned by the supplied user
          content: 
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/repository'
          links:
            userRepository:
              $ref: '#/components/links/UserRepository'
  /2.0/repositories/{username}/{slug}: 
    get: 
      operationId: getRepository
      parameters: 
        - name: username
          in: path
          required: true
          schema:
            type: string
        - name: slug
          in: path
          required: true
          schema:
            type: string
      responses: 
        '200':
          description: The repository
          content:
              application/json: 
                schema: 
                  $ref: '#/components/schemas/repository'
          links:
            repositoryPullRequests:
              $ref: '#/components/links/RepositoryPullRequests'
  /2.0/repositories/{username}/{slug}/pullrequests: 
    get: 
      operationId: getPullRequestsByRepository
      parameters: 
      - name: username
        in: path
        required: true
        schema:
          type: string
      - name: slug
        in: path
        required: true
        schema:
          type: string
      - name: state
        in: query
        schema:
          type: string
          enum: 
            - open
            - merged
            - declined
      responses: 
        '200':
          description: an array of pull request objects
          content:
            application/json: 
              schema: 
                type: array
                items: 
                  $ref: '#/components/schemas/pullrequest'
  /2.0/repositories/{username}/{slug}/pullrequests/{pid}: 
    get: 
      operationId: getPullRequestsById
      parameters: 
      - name: username
        in: path
        required: true
        schema:
          type: string
      - name: slug
        in: path
        required: true
        schema:
          type: string
      - name: pid
        in: path
        required: true
        schema:
          type: string
      responses: 
        '200':
          description: a pull request object
          content:
            application/json: 
              schema: 
                $ref: '#/components/schemas/pullrequest'
          links:
            pullRequestMerge:
              $ref: '#/components/links/PullRequestMerge'
  /2.0/repositories/{username}/{slug}/pullrequests/{pid}/merge: 
    post: 
      operationId: mergePullRequest
      parameters: 
      - name: username
        in: path
        required: true
        schema:
          type: string
      - name: slug
        in: path
        required: true
        schema:
          type: string
      - name: pid
        in: path
        required: true
        schema:
          type: string
      responses: 
        '204':
          description: the PR was successfully merged
components:
  links:
    UserRepositories:
      # returns array of '#/components/schemas/repository'
      operationId: getRepositoriesByOwner
      parameters:
        username: $response.body#/username
    UserRepository:
      # returns '#/components/schemas/repository'
      operationId: getRepository
      parameters:
        username: $response.body#/owner/username
        slug: $response.body#/slug
    RepositoryPullRequests:
      # returns '#/components/schemas/pullrequest'
      operationId: getPullRequestsByRepository
      parameters: 
          username: $response.body#/owner/username
          slug: $response.body#/slug
    PullRequestMerge:
      # executes /2.0/repositories/{username}/{slug}/pullrequests/{pid}/merge
      operationId: mergePullRequest
      parameters:
        username: $response.body#/author/username
        slug: $response.body#/repository/slug
        pid: $response.body#/id
  schemas: 
    user: 
      type: object
      properties: 
        username: 
          type: string
        uuid: 
          type: string
    repository: 
      type: object
      properties: 
        slug: 
          type: string
        owner: 
          $ref: '#/components/schemas/user'
    pullrequest: 
      type: object
      properties: 
        id: 
          type: integer
        title: 
          type: string
        repository: 
          $ref: '#/components/schemas/repository'
        author: 
          $ref: '#/components/schemas/user'
