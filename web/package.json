{"name": "postman2openapi-web", "version": "0.1.0", "main": "index.js", "private": true, "scripts": {"build": "rimraf dist wasm && NODE_OPTIONS=--openssl-legacy-provider webpack --config webpack.config.js --mode production", "start": "rimraf dist wasm && NODE_OPTIONS=--openssl-legacy-provider webpack-dev-server --mode development", "test": "cargo test && wasm-pack test --headless"}, "dependencies": {"clean-webpack-plugin": "^4.0.0", "codemirror": "^5.57.0", "js-yaml": "^4.1.0", "postman2openapi": "file:./wasm"}, "devDependencies": {"@types/codemirror": "0.0.97", "@wasm-tool/wasm-pack-plugin": "^1.1.0", "copy-webpack-plugin": "^5.0.3", "css-loader": "^4.2.2", "file-loader": "^6.0.0", "rimraf": "^3.0.0", "style-loader": "^1.2.1", "webpack": "^4.42.3", "webpack-cli": "^3.3.3", "webpack-dev-server": "^3.7.1"}}