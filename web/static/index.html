<!DOCTYPE html>
<html>
  <head>
    <title>postman2openapi - Transform Postman Collections into OpenAPI</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta
      name="description"
      content="Convert Postman Collections into OpenAPI definitions in your browser."
    />
    <meta
      name="keywords"
      content="postman,collections,openapi,online,converter,transformer,transpiler"
    />

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/bootstrap/3.3.6/css/bootstrap.css"
    />
  </head>

  <body>
    <div class="header">
      <h1>Transform Postman Collections into OpenAPI</h1>
      <h2>postman2openapi</h2>
    </div>
    <div class="content">
      <div class="src">
        <h4 class="subheader">Postman Collection JSON:</h4>
        <textarea id="postman"> </textarea>
      </div>
      <div class="dst">
        <h4 class="subheader">OpenAPI:</h4>
        <button id="openapi-copy-btn" class="btn">Copy</button>
        <textarea id="openapi"></textarea>
      </div>
    </div>

    <div class="gh-ribbon">
      <a
        href="https://github.com/kevinswiber/postman2openapi"
        target="_blank"
        title="Source code for postman2openapi."
        >Fork me on GitHub</a
      >
    </div>
    <noscript
      >This page contains webassembly and javascript content, please enable
      javascript in your browser.</noscript
    >
    <script src="index.js"></script>
  </body>
</html>
