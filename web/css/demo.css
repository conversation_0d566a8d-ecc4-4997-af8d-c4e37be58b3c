html, body {
  height: 100%;
}

body {
  margin: 0 30px;
  padding-bottom: 125px;
  overflow-x: hidden;
}

.header {
  padding: 1px 0 30px;
  white-space: nowrap;
  overflow: hidden;
}

.subheader {
  position: absolute;
  top: -35px;
  width: 100%;
}

.content {
  height: 100%;
}

#permalink {
  float: right;
}

.src, .dst {
  width: 49%;
  position: relative;
  float: left;
  height: 100%;
}

.dst {
  float: right;
}

.src .CodeMirror {
  background-color: #fffffb;
  border: 1px solid #888;
  border-radius: 3px;
  box-shadow: 0 0 3px #ccc inset;
}

.dst .CodeMirror {
  background-color: #F8F8F8;
  border: 1px solid #eee;
  border-radius: 3px;
}

/*.CodeMirror-scroll {
  height: 500px;
  line-height: 1.2;
}*/

.CodeMirror {
  height: 100%;
  font-size: 13px;
}

.CodeMirror .cm-comment {
  color: #baa;
}

.gh-ribbon {
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    background-color: #686868;
    box-shadow: 0 0 2px rgba(102, 102, 102, 0.4);
    display: block;
    padding: 1px 0;
    position: fixed;
    right: -60px;
    top: 44px;
    width: 230px;
    z-index: 10000;
}
.gh-ribbon a {
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 13px;
    border: 1px solid #AAAAAA;
    color: #FFFFFF;
    display: block;
    font-size: 13px;
    font-weight: 700;
    outline: medium none;
    padding: 4px 50px 2px;
    text-align: center;
    text-decoration: none;
}
.csstransforms .gh-ribbon {
    display: block;
}


.btn {
    border: 1px solid #AAAAAA;
    outline: none;
    font-size: 13px;
    background-color: #FFFFFF;
    border-radius: 5px;
    font-weight: 600;
    position: absolute;
    right: 5px;
    top:5px;
    z-index: 10;
    padding: 3px 5px;
}

.btn:hover {
    background-color: #EEEEEE;
}